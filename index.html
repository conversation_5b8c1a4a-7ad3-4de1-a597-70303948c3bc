<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">机房运行概况</h1>
                    <p class="page-subtitle">实时监控机房运行状态，智能化管理各项设施设备</p>
                </div>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                正常
                            </div>
                        </div>
                        <div class="stat-value" data-realtime="temperature">24°C</div>
                        <div class="stat-label">环境状态</div>
                        <div class="stat-change">温度: 22°C | 湿度: 45%</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                稳定
                            </div>
                        </div>
                        <div class="stat-value" data-realtime="power">1.42</div>
                        <div class="stat-label">电力系统</div>
                        <div class="stat-change">PUE值 | 实时平均</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon info">
                                <i class="fas fa-wind"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                运行中
                            </div>
                        </div>
                        <div class="stat-value" data-realtime="hvac">运行中</div>
                        <div class="stat-label">空调系统</div>
                        <div class="stat-change">3台运行 | 1台备用</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-status warning">
                                <div class="stat-status-dot"></div>
                                监控中
                            </div>
                        </div>
                        <div class="stat-value">1.42</div>
                        <div class="stat-label">能耗效率</div>
                        <div class="stat-change">PUE值 | 实时平均</div>
                    </div>
                </div>
                
                <!-- 主要功能区域 -->
                <div class="grid grid-cols-3 gap-6">
                    <!-- 系统状态监控 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">系统状态</h3>
                            <a href="bas.html" class="btn btn-outline">查看详情</a>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-snowflake text-blue-500"></i>
                                        <span>空调系统</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        正常运行
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-lightbulb text-yellow-500"></i>
                                        <span>照明系统</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        正常运行
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-wind text-green-500"></i>
                                        <span>通风系统</span>
                                    </div>
                                    <span class="status warning">
                                        <span class="status-dot"></span>
                                        维护中
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-elevator text-purple-500"></i>
                                        <span>电梯系统</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        正常运行
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 安全监控 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">安全监控</h3>
                            <a href="security.html" class="btn btn-outline">查看详情</a>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-video text-blue-500"></i>
                                        <span>视频监控</span>
                                    </div>
                                    <span class="text-sm text-gray-600">48/50 在线</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-door-open text-green-500"></i>
                                        <span>门禁系统</span>
                                    </div>
                                    <span class="text-sm text-gray-600">正常</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-user-friends text-orange-500"></i>
                                        <span>今日访客</span>
                                    </div>
                                    <span class="text-sm text-gray-600">23 人</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-shield-alt text-red-500"></i>
                                        <span>安全告警</span>
                                    </div>
                                    <span class="text-sm text-red-600">2 条</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 能源管理 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">能源管理</h3>
                            <a href="energy.html" class="btn btn-outline">查看详情</a>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-plug text-yellow-500"></i>
                                        <span>用电量</span>
                                    </div>
                                    <span class="text-sm text-gray-600">1,024 kWh</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-tint text-blue-500"></i>
                                        <span>用水量</span>
                                    </div>
                                    <span class="text-sm text-gray-600">156 m³</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-fire text-red-500"></i>
                                        <span>燃气用量</span>
                                    </div>
                                    <span class="text-sm text-gray-600">89 m³</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-leaf text-green-500"></i>
                                        <span>节能率</span>
                                    </div>
                                    <span class="text-sm text-green-600">15.2%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作和最新动态 -->
                <div class="grid grid-cols-2 gap-6 mt-6">
                    <!-- 快速操作 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">快速操作</h3>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-2 gap-4">
                                <a href="visitor.html" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i>
                                    访客登记
                                </a>
                                <a href="meeting.html" class="btn btn-success">
                                    <i class="fas fa-calendar-plus"></i>
                                    会议预约
                                </a>
                                <a href="parking.html" class="btn btn-warning">
                                    <i class="fas fa-car"></i>
                                    停车管理
                                </a>
                                <a href="maintenance.html" class="btn btn-danger">
                                    <i class="fas fa-tools"></i>
                                    报修工单
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 最新动态 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最新动态</h3>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="flex items-start gap-3">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-800">空调系统完成定期维护</p>
                                        <p class="text-xs text-gray-500">2分钟前</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-3">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-800">访客张三已通过门禁验证</p>
                                        <p class="text-xs text-gray-500">5分钟前</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-3">
                                    <div class="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-800">会议室A预约成功</p>
                                        <p class="text-xs text-gray-500">10分钟前</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-3">
                                    <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-800">停车场车位不足告警</p>
                                        <p class="text-xs text-gray-500">15分钟前</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化首页
        SmartBuilding.initializePage('仪表板', []);
        
        // 添加一些首页特有的功能
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟实时数据更新
            setInterval(updateDashboardData, 10000);
        });
        
        function updateDashboardData() {
            // 更新统计数据
            const tempElement = document.querySelector('[data-realtime="temperature"]');
            const powerElement = document.querySelector('[data-realtime="power"]');
            const peopleElement = document.querySelector('[data-realtime="people"]');
            
            if (tempElement) {
                tempElement.textContent = SmartBuilding.generateRandomData(20, 28) + '°C';
            }
            if (powerElement) {
                powerElement.textContent = SmartBuilding.formatNumber(SmartBuilding.generateRandomData(800, 1200)) + ' kW';
            }
            if (peopleElement) {
                peopleElement.textContent = SmartBuilding.generateRandomData(150, 300);
            }
        }
    </script>
</body>
</html>
