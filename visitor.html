<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访客管理系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="top-bar">
        <div class="breadcrumb">
            <a href="index.html" class="logo">
                <i class="fas fa-building"></i>
                <span>智慧楼宇管理系统</span>
            </a>
        </div>
        <div class="user-menu">
            <button class="notification-btn">
                <i class="fas fa-bell"></i>
                <span class="notification-badge"></span>
            </button>
            <div class="user-profile">
                <div class="user-avatar">管</div>
                <div class="user-info">
                    <div class="user-name">管理员</div>
                    <div class="user-role">系统管理员</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <a href="index.html" class="logo">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </div>

            <nav class="nav-menu">
                <div class="nav-group">
                    <div class="nav-group-title">系统监控</div>
                    <a href="index.html" class="nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>系统概览</span>
                    </a>
                    <a href="bas.html" class="nav-item">
                        <i class="fas fa-cogs"></i>
                        <span>楼宇自控</span>
                    </a>
                    <a href="energy.html" class="nav-item">
                        <i class="fas fa-bolt"></i>
                        <span>能耗管理</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">安全管理</div>
                    <a href="security.html" class="nav-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>安防系统</span>
                    </a>
                    <a href="access.html" class="nav-item">
                        <i class="fas fa-key"></i>
                        <span>门禁管理</span>
                    </a>
                    <a href="elevator.html" class="nav-item">
                        <i class="fas fa-elevator"></i>
                        <span>梯控管理</span>
                    </a>
                    <a href="visitor.html" class="nav-item active">
                        <i class="fas fa-users"></i>
                        <span>访客管理</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">设施服务</div>
                    <a href="parking.html" class="nav-item">
                        <i class="fas fa-car"></i>
                        <span>停车管理</span>
                    </a>
                    <a href="alarm.html" class="nav-item">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>报警管理</span>
                    </a>
                    <a href="meeting.html" class="nav-item">
                        <i class="fas fa-calendar"></i>
                        <span>会议室</span>
                    </a>
                    <a href="maintenance.html" class="nav-item">
                        <i class="fas fa-wrench"></i>
                        <span>设备维护</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">系统管理</div>
                    <a href="settings.html" class="nav-item">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">访客管理系统</h1>
                    <p class="page-subtitle">智能访客预约、登记、通行管理一体化解决方案</p>
                </div>
                
                <!-- 访客概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-user-friends"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                正常
                            </div>
                        </div>
                        <div class="stat-value">23</div>
                        <div class="stat-label">今日访客</div>
                        <div class="stat-change">较昨日 +5</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                实时
                            </div>
                        </div>
                        <div class="stat-value">8</div>
                        <div class="stat-label">当前在访</div>
                        <div class="stat-change">实时统计</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-status warning">
                                <div class="stat-status-dot"></div>
                                待处理
                            </div>
                        </div>
                        <div class="stat-value">15</div>
                        <div class="stat-label">预约待审</div>
                        <div class="stat-change">需要处理</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="stat-status warning">
                                <div class="stat-status-dot"></div>
                                异常
                            </div>
                        </div>
                        <div class="stat-value">2</div>
                        <div class="stat-label">超时未离</div>
                        <div class="stat-change">需要关注</div>
                    </div>
                </div>
                
                <!-- 快速操作和访客登记 -->
                <div class="grid grid-cols-3 gap-6 mb-6">
                    <!-- 快速访客登记 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-plus mr-2"></i>
                                快速访客登记
                            </h3>
                        </div>
                        <div class="card-body">
                            <form class="space-y-4">
                                <div>
                                    <label class="form-label">访客姓名</label>
                                    <input type="text" class="form-input" placeholder="请输入访客姓名" required>
                                </div>
                                <div>
                                    <label class="form-label">联系电话</label>
                                    <input type="tel" class="form-input" placeholder="请输入手机号码" required>
                                </div>
                                <div>
                                    <label class="form-label">被访人</label>
                                    <select class="form-select" required>
                                        <option value="">请选择被访人</option>
                                        <option value="zhang">张三 - 技术部</option>
                                        <option value="li">李四 - 市场部</option>
                                        <option value="wang">王五 - 人事部</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="form-label">访问目的</label>
                                    <select class="form-select" required>
                                        <option value="">请选择访问目的</option>
                                        <option value="business">商务洽谈</option>
                                        <option value="interview">面试</option>
                                        <option value="meeting">会议</option>
                                        <option value="delivery">送货</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="form-label">预计停留时间</label>
                                    <select class="form-select" required>
                                        <option value="">请选择时长</option>
                                        <option value="30">30分钟</option>
                                        <option value="60">1小时</option>
                                        <option value="120">2小时</option>
                                        <option value="240">4小时</option>
                                        <option value="480">全天</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-full">
                                    <i class="fas fa-check"></i>
                                    立即登记
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 当前在访访客 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users mr-2"></i>
                                当前在访访客
                            </h3>
                            <span class="badge success">8人</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="visitor-item">
                                    <div class="flex items-center gap-3">
                                        <div class="visitor-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="visitor-name">张三</div>
                                            <div class="visitor-detail">访问李四 · 商务洽谈</div>
                                            <div class="visitor-time">入访时间: 09:30</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge success">在访</span>
                                            <button class="btn btn-outline btn-xs">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="visitor-item">
                                    <div class="flex items-center gap-3">
                                        <div class="visitor-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="visitor-name">李四</div>
                                            <div class="visitor-detail">访问王五 · 面试</div>
                                            <div class="visitor-time">入访时间: 10:15</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge success">在访</span>
                                            <button class="btn btn-outline btn-xs">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="visitor-item">
                                    <div class="flex items-center gap-3">
                                        <div class="visitor-avatar warning">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="visitor-name">王五</div>
                                            <div class="visitor-detail">访问赵六 · 会议</div>
                                            <div class="visitor-time">入访时间: 08:45</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge warning">超时</span>
                                            <button class="btn btn-warning btn-xs">
                                                <i class="fas fa-phone"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="visitor-item">
                                    <div class="flex items-center gap-3">
                                        <div class="visitor-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="visitor-name">赵六</div>
                                            <div class="visitor-detail">访问孙七 · 送货</div>
                                            <div class="visitor-time">入访时间: 11:20</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge success">在访</span>
                                            <button class="btn btn-outline btn-xs">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预约审批 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clipboard-check mr-2"></i>
                                预约审批
                            </h3>
                            <span class="badge warning">15 待审</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="appointment-item">
                                    <div class="flex items-start gap-3">
                                        <div class="appointment-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="appointment-name">陈八</div>
                                            <div class="appointment-detail">预约访问张三 · 商务洽谈</div>
                                            <div class="appointment-time">预约时间: 明天 14:00</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <button class="btn btn-success btn-xs">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-xs">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="appointment-item">
                                    <div class="flex items-start gap-3">
                                        <div class="appointment-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="appointment-name">周九</div>
                                            <div class="appointment-detail">预约访问李四 · 面试</div>
                                            <div class="appointment-time">预约时间: 今天 16:30</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <button class="btn btn-success btn-xs">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-xs">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="appointment-item">
                                    <div class="flex items-start gap-3">
                                        <div class="appointment-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="appointment-name">吴十</div>
                                            <div class="appointment-detail">预约访问王五 · 会议</div>
                                            <div class="appointment-time">预约时间: 后天 09:00</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <button class="btn btn-success btn-xs">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-xs">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="appointment-item">
                                    <div class="flex items-start gap-3">
                                        <div class="appointment-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="appointment-name">郑十一</div>
                                            <div class="appointment-detail">预约访问赵六 · 其他</div>
                                            <div class="appointment-time">预约时间: 今天 15:00</div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <button class="btn btn-success btn-xs">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-xs">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 访客记录和统计分析 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 访客记录 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-history mr-2"></i>
                                访客记录
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-filter"></i>
                                    筛选
                                </button>
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-download"></i>
                                    导出
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="overflow-x-auto">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>访客姓名</th>
                                            <th>被访人</th>
                                            <th>访问时间</th>
                                            <th>离开时间</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>张三</td>
                                            <td>李四</td>
                                            <td>09:30</td>
                                            <td>--</td>
                                            <td><span class="badge success">在访</span></td>
                                        </tr>
                                        <tr>
                                            <td>李四</td>
                                            <td>王五</td>
                                            <td>10:15</td>
                                            <td>--</td>
                                            <td><span class="badge success">在访</span></td>
                                        </tr>
                                        <tr>
                                            <td>王五</td>
                                            <td>赵六</td>
                                            <td>08:45</td>
                                            <td>--</td>
                                            <td><span class="badge warning">超时</span></td>
                                        </tr>
                                        <tr>
                                            <td>赵六</td>
                                            <td>孙七</td>
                                            <td>08:00</td>
                                            <td>09:30</td>
                                            <td><span class="badge primary">已离开</span></td>
                                        </tr>
                                        <tr>
                                            <td>孙七</td>
                                            <td>周八</td>
                                            <td>07:30</td>
                                            <td>08:45</td>
                                            <td><span class="badge primary">已离开</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计分析 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar mr-2"></i>
                                统计分析
                            </h3>
                            <select class="form-select" style="width: auto;">
                                <option>本周</option>
                                <option>本月</option>
                                <option>本季度</option>
                            </select>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 访问目的统计 -->
                                <div>
                                    <h4 class="font-medium mb-2">访问目的分布</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-blue-500 rounded"></div>
                                                <span class="text-sm">商务洽谈</span>
                                            </div>
                                            <span class="text-sm font-medium">45%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 45%"></div>
                                        </div>
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-green-500 rounded"></div>
                                                <span class="text-sm">面试</span>
                                            </div>
                                            <span class="text-sm font-medium">25%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar success" style="width: 25%"></div>
                                        </div>
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                                                <span class="text-sm">会议</span>
                                            </div>
                                            <span class="text-sm font-medium">20%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar warning" style="width: 20%"></div>
                                        </div>
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-red-500 rounded"></div>
                                                <span class="text-sm">其他</span>
                                            </div>
                                            <span class="text-sm font-medium">10%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar danger" style="width: 10%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 时间段统计 -->
                                <div>
                                    <h4 class="font-medium mb-2">访问时间段分布</h4>
                                    <div class="grid grid-cols-2 gap-2 text-sm">
                                        <div class="flex justify-between">
                                            <span>08:00-10:00</span>
                                            <span class="font-medium">35%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>10:00-12:00</span>
                                            <span class="font-medium">28%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>14:00-16:00</span>
                                            <span class="font-medium">25%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>16:00-18:00</span>
                                            <span class="font-medium">12%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 部门统计 */
                                <div>
                                    <h4 class="font-medium mb-2">被访部门排行</h4>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span>技术部</span>
                                            <span class="font-medium">42人次</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>市场部</span>
                                            <span class="font-medium">28人次</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>人事部</span>
                                            <span class="font-medium">15人次</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>财务部</span>
                                            <span class="font-medium">8人次</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 表单提交处理
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('访客登记成功！');
            this.reset();
        });
        
        // 审批按钮处理
        document.querySelectorAll('.btn-success.btn-xs').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.innerHTML.includes('check')) {
                    alert('预约已批准');
                    this.closest('.appointment-item').style.opacity = '0.5';
                }
            });
        });
        
        document.querySelectorAll('.btn-danger.btn-xs').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.innerHTML.includes('times')) {
                    alert('预约已拒绝');
                    this.closest('.appointment-item').style.opacity = '0.5';
                }
            });
        });
    </script>
    
    <style>
        /* 表单样式 */
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .form-input,
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        /* 访客项样式 */
        .visitor-item,
        .appointment-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .visitor-avatar,
        .appointment-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .visitor-avatar.warning {
            background: var(--warning-color);
        }
        
        .visitor-name,
        .appointment-name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .visitor-detail,
        .appointment-detail {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .visitor-time,
        .appointment-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 10px;
        }
        
        .w-full {
            width: 100%;
        }
        
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
        
        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }
        
        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .overflow-x-auto {
            overflow-x: auto;
        }
        
        .flex {
            display: flex;
        }
        
        .flex-col {
            flex-direction: column;
        }
        
        .items-center {
            align-items: center;
        }
        
        .items-start {
            align-items: flex-start;
        }
        
        .justify-between {
            justify-content: space-between;
        }
        
        .gap-1 {
            gap: 0.25rem;
        }
        
        .gap-2 {
            gap: 0.5rem;
        }
        
        .gap-3 {
            gap: 0.75rem;
        }
        
        .flex-1 {
            flex: 1;
        }
    </style>
</body>
</html>
