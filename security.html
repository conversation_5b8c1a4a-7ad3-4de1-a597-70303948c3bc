<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">视频监控系统</h1>
                    <p class="page-subtitle">实时监控楼宇安全，AI智能分析异常行为</p>
                </div>
                
                <!-- 监控概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-video"></i>
                            </div>
                        </div>
                        <div class="stat-value">48/50</div>
                        <div class="stat-label">摄像头在线</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>96% 在线率</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value">3</div>
                        <div class="stat-label">今日告警</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-up"></i>
                            <span>1 新增</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value" data-realtime="people">234</div>
                        <div class="stat-label">当前人数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>正常范围</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                        <div class="stat-value">99.2%</div>
                        <div class="stat-label">安全指数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>优秀</span>
                        </div>
                    </div>
                </div>
                
                <!-- 主监控区域 -->
                <div class="grid grid-cols-3 gap-6 mb-6">
                    <!-- 主监控画面 -->
                    <div class="card col-span-2">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-desktop mr-2"></i>
                                主监控画面
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-expand"></i>
                                    全屏
                                </button>
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-record-vinyl"></i>
                                    录制
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <!-- 监控画面网格 -->
                            <div class="grid grid-cols-2 gap-1 h-96">
                                <div class="video-panel active" data-camera="CAM001">
                                    <div class="video-placeholder">
                                        <i class="fas fa-video text-4xl mb-2"></i>
                                        <p class="text-sm">大厅主入口</p>
                                        <p class="text-xs text-gray-500">CAM001</p>
                                    </div>
                                    <div class="video-overlay">
                                        <span class="status-indicator online"></span>
                                        <span class="camera-name">大厅主入口</span>
                                    </div>
                                </div>
                                <div class="video-panel" data-camera="CAM002">
                                    <div class="video-placeholder">
                                        <i class="fas fa-video text-4xl mb-2"></i>
                                        <p class="text-sm">停车场入口</p>
                                        <p class="text-xs text-gray-500">CAM002</p>
                                    </div>
                                    <div class="video-overlay">
                                        <span class="status-indicator online"></span>
                                        <span class="camera-name">停车场入口</span>
                                    </div>
                                </div>
                                <div class="video-panel" data-camera="CAM003">
                                    <div class="video-placeholder">
                                        <i class="fas fa-video text-4xl mb-2"></i>
                                        <p class="text-sm">电梯厅</p>
                                        <p class="text-xs text-gray-500">CAM003</p>
                                    </div>
                                    <div class="video-overlay">
                                        <span class="status-indicator warning"></span>
                                        <span class="camera-name">电梯厅</span>
                                    </div>
                                </div>
                                <div class="video-panel" data-camera="CAM004">
                                    <div class="video-placeholder">
                                        <i class="fas fa-video text-4xl mb-2"></i>
                                        <p class="text-sm">办公区走廊</p>
                                        <p class="text-xs text-gray-500">CAM004</p>
                                    </div>
                                    <div class="video-overlay">
                                        <span class="status-indicator offline"></span>
                                        <span class="camera-name">办公区走廊</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 摄像头列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list mr-2"></i>
                                摄像头列表
                            </h3>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="card-body p-0">
                            <div class="camera-list">
                                <div class="camera-item active" data-camera="CAM001">
                                    <div class="camera-info">
                                        <div class="camera-name">大厅主入口</div>
                                        <div class="camera-id">CAM001</div>
                                    </div>
                                    <span class="status-indicator online"></span>
                                </div>
                                <div class="camera-item" data-camera="CAM002">
                                    <div class="camera-info">
                                        <div class="camera-name">停车场入口</div>
                                        <div class="camera-id">CAM002</div>
                                    </div>
                                    <span class="status-indicator online"></span>
                                </div>
                                <div class="camera-item" data-camera="CAM003">
                                    <div class="camera-info">
                                        <div class="camera-name">电梯厅</div>
                                        <div class="camera-id">CAM003</div>
                                    </div>
                                    <span class="status-indicator warning"></span>
                                </div>
                                <div class="camera-item" data-camera="CAM004">
                                    <div class="camera-info">
                                        <div class="camera-name">办公区走廊</div>
                                        <div class="camera-id">CAM004</div>
                                    </div>
                                    <span class="status-indicator offline"></span>
                                </div>
                                <div class="camera-item" data-camera="CAM005">
                                    <div class="camera-info">
                                        <div class="camera-name">会议室区域</div>
                                        <div class="camera-id">CAM005</div>
                                    </div>
                                    <span class="status-indicator online"></span>
                                </div>
                                <div class="camera-item" data-camera="CAM006">
                                    <div class="camera-info">
                                        <div class="camera-name">安全出口</div>
                                        <div class="camera-id">CAM006</div>
                                    </div>
                                    <span class="status-indicator online"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- AI分析和告警记录 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- AI智能分析 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-brain mr-2"></i>
                                AI智能分析
                            </h3>
                            <span class="badge success">实时分析</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <div class="ai-detection-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-walking text-blue-500"></i>
                                            <div>
                                                <div class="font-medium">人员行为分析</div>
                                                <div class="text-sm text-gray-500">检测异常行为模式</div>
                                            </div>
                                        </div>
                                        <span class="badge success">正常</span>
                                    </div>
                                </div>
                                
                                <div class="ai-detection-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-users text-green-500"></i>
                                            <div>
                                                <div class="font-medium">人群密度监测</div>
                                                <div class="text-sm text-gray-500">当前密度: 中等</div>
                                            </div>
                                        </div>
                                        <span class="badge warning">注意</span>
                                    </div>
                                </div>
                                
                                <div class="ai-detection-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-exclamation-triangle text-orange-500"></i>
                                            <div>
                                                <div class="font-medium">越界检测</div>
                                                <div class="text-sm text-gray-500">监控禁入区域</div>
                                            </div>
                                        </div>
                                        <span class="badge success">正常</span>
                                    </div>
                                </div>
                                
                                <div class="ai-detection-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-clock text-red-500"></i>
                                            <div>
                                                <div class="font-medium">滞留检测</div>
                                                <div class="text-sm text-gray-500">检测长时间滞留</div>
                                            </div>
                                        </div>
                                        <span class="badge danger">告警</span>
                                    </div>
                                </div>
                                
                                <div class="ai-detection-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-mask text-purple-500"></i>
                                            <div>
                                                <div class="font-medium">口罩检测</div>
                                                <div class="text-sm text-gray-500">佩戴率: 95%</div>
                                            </div>
                                        </div>
                                        <span class="badge success">正常</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 告警记录 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bell mr-2"></i>
                                告警记录
                            </h3>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-filter"></i>
                                筛选
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="alert-item">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon danger">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">人员长时间滞留</div>
                                            <div class="alert-desc">电梯厅有人员滞留超过10分钟</div>
                                            <div class="alert-time">2分钟前 · CAM003</div>
                                        </div>
                                        <button class="btn btn-outline btn-sm">处理</button>
                                    </div>
                                </div>
                                
                                <div class="alert-item">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon warning">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">人群密度过高</div>
                                            <div class="alert-desc">大厅区域人员密度超过安全阈值</div>
                                            <div class="alert-time">15分钟前 · CAM001</div>
                                        </div>
                                        <button class="btn btn-success btn-sm">已处理</button>
                                    </div>
                                </div>
                                
                                <div class="alert-item">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon info">
                                            <i class="fas fa-video"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">摄像头离线</div>
                                            <div class="alert-desc">办公区走廊摄像头连接中断</div>
                                            <div class="alert-time">1小时前 · CAM004</div>
                                        </div>
                                        <button class="btn btn-outline btn-sm">检修</button>
                                    </div>
                                </div>
                                
                                <div class="alert-item">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon success">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">系统自检完成</div>
                                            <div class="alert-desc">所有摄像头系统自检正常</div>
                                            <div class="alert-time">2小时前 · 系统</div>
                                        </div>
                                        <span class="text-sm text-gray-500">已完成</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化安全监控页面
        SmartBuilding.initializePage('视频监控系统', ['安全管理', '视频监控']);
        
        // 摄像头切换功能
        document.querySelectorAll('.camera-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.camera-item').forEach(i => i.classList.remove('active'));
                document.querySelectorAll('.video-panel').forEach(p => p.classList.remove('active'));
                
                // 添加当前活动状态
                this.classList.add('active');
                const cameraId = this.dataset.camera;
                const videoPanel = document.querySelector(`[data-camera="${cameraId}"]`);
                if (videoPanel) {
                    videoPanel.classList.add('active');
                }
            });
        });
    </script>
    
    <style>
        /* 视频面板样式 */
        .video-panel {
            position: relative;
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .video-panel.active {
            border: 2px solid var(--primary-light);
        }
        
        .video-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            text-align: center;
        }
        
        .video-overlay {
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(0, 0, 0, 0.7);
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }
        
        /* 摄像头列表样式 */
        .camera-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .camera-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .camera-item:hover {
            background-color: #f8f9fa;
        }
        
        .camera-item.active {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid var(--primary-light);
        }
        
        .camera-info .camera-name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .camera-info .camera-id {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-indicator.online {
            background-color: var(--success-color);
        }
        
        .status-indicator.warning {
            background-color: var(--warning-color);
        }
        
        .status-indicator.offline {
            background-color: var(--danger-color);
        }
        
        /* AI检测项样式 */
        .ai-detection-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        /* 告警项样式 */
        .alert-item {
            padding: 12px;
            border-left: 3px solid transparent;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .alert-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .alert-icon.danger {
            background-color: var(--danger-color);
        }
        
        .alert-icon.warning {
            background-color: var(--warning-color);
        }
        
        .alert-icon.info {
            background-color: var(--primary-light);
        }
        
        .alert-icon.success {
            background-color: var(--success-color);
        }
        
        .alert-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .alert-desc {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .alert-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
        
        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .col-span-2 {
            grid-column: span 2;
        }
    </style>
</body>
</html>
