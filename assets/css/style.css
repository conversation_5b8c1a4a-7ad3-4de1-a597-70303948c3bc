/* 智慧楼宇管理系统 - 清爽紫色主题 - Updated 2025-06-23 */
:root {
  /* 主色调 - 紫色渐变主题 */
  --primary-color: #8b5cf6;
  --primary-dark: #7c3aed;
  --primary-light: #a78bfa;
  --primary-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  --primary-gradient-hover: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);

  /* 顶部栏紫色渐变 */
  --header-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);

  /* 辅助色彩 - 更柔和的配色 */
  --success-color: #22c55e;
  --success-light: #4ade80;
  --success-bg: rgba(34, 197, 94, 0.1);

  --warning-color: #f59e0b;
  --warning-light: #fbbf24;
  --warning-bg: rgba(245, 158, 11, 0.1);

  --danger-color: #ef4444;
  --danger-light: #f87171;
  --danger-bg: rgba(239, 68, 68, 0.1);

  --info-color: #3b82f6;
  --info-light: #60a5fa;
  --info-bg: rgba(59, 130, 246, 0.1);

  /* 背景色彩 - 清爽白色主题 */
  --body-bg: #f8fafc;
  --sidebar-bg: #ffffff;
  --card-bg: #ffffff;
  --card-bg-hover: #ffffff;

  /* 文字色彩 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --text-white: #ffffff;
  --text-muted: #d1d5db;

  /* 边框和分割线 */
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --border-primary: rgba(139, 92, 246, 0.2);

  /* 阴影系统 - 更轻柔的阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角 */
  --radius-sm: 6px;
  --radius: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* 动画时长 */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 容器布局 */
.container {
  display: flex;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
}

.container .main-content {
  flex: 1;
  margin-left: 0;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  min-height: auto;
}

.container .sidebar {
  position: relative;
  top: 0;
  height: auto;
  min-height: calc(100vh - 60px);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: var(--body-bg);
  color: var(--text-primary);
  line-height: 1.5;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 14px;
}

/* 布局容器 */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* 侧边栏样式 - 简洁白色设计 */
.sidebar {
  width: 240px;
  background: var(--sidebar-bg);
  color: var(--text-primary);
  position: fixed;
  height: 100vh;
  top: 60px;
  overflow-y: auto;
  z-index: 999;
  transition: all var(--transition-normal) ease;
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.sidebar-header {
  padding: 20px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--sidebar-bg);
}

.sidebar .logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
  transition: all var(--transition-normal) ease;
}

.sidebar .logo:hover {
  color: var(--primary-color);
}

.sidebar .logo i {
  font-size: 20px;
  color: var(--primary-color);
}

/* 导航菜单 - 简洁设计 */
.nav-menu {
  padding: 16px 0;
}

.nav-group {
  margin-bottom: 24px;
}

.nav-group-title {
  padding: 8px 20px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal) ease;
  font-weight: 400;
  font-size: 14px;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  color: var(--primary-color);
  background: var(--border-light);
  border-left-color: var(--primary-color);
}

.nav-item.active {
  color: var(--primary-color);
  background: rgba(139, 92, 246, 0.08);
  border-left-color: var(--primary-color);
  font-weight: 500;
}

.nav-item i {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* 主内容区 */
.main-content {
  flex: 1;
  margin-left: 240px;
  margin-top: 60px;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px);
}

/* 顶部栏 - 紫色渐变设计 */
.top-bar {
  height: 60px;
  background: var(--header-gradient);
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all var(--transition-normal) ease;
}

/* 顶部栏logo区域 */
.top-bar .logo {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-white);
  font-size: 18px;
  font-weight: 600;
}

.top-bar .logo i {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.9);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 400;
}

.breadcrumb a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.breadcrumb a:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-btn {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--radius);
  transition: all var(--transition-normal) ease;
  backdrop-filter: blur(10px);
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  transform: translateY(-1px);
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  background: var(--danger-color);
  border-radius: 50%;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: var(--radius);
  transition: all var(--transition-normal) ease;
}

.user-profile:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: 600;
  font-size: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all var(--transition-normal) ease;
}

.user-profile:hover .user-avatar {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-white);
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 内容区域 - 宽松布局 */
.content {
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: var(--body-bg);
  position: relative;
}

/* 页面标题 - 简洁设计 */
.page-header {
  margin-bottom: 40px;
  padding: 0;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  letter-spacing: -0.01em;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}

/* 卡片组件 - 简洁设计 */
.card {
  background: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all var(--transition-normal) ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 24px;
}

/* 网格布局 - 宽松间距 */
.grid {
  display: grid;
  gap: 32px;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* 间距和排版工具类 */
.mb-6 { margin-bottom: 48px; }
.mt-6 { margin-top: 48px; }
.space-y-4 > * + * { margin-top: 24px; }
.space-y-3 > * + * { margin-top: 20px; }

/* 文字样式 */
.text-sm { font-size: 12px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }
.text-xl { font-size: 18px; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-gray-600 { color: var(--text-secondary); }
.text-gray-800 { color: var(--text-primary); }
.text-gray-500 { color: var(--text-light); }
.text-blue-500 { color: var(--info-color); }
.text-green-500 { color: var(--success-color); }
.text-yellow-500 { color: var(--warning-color); }
.text-red-500 { color: var(--danger-color); }
.text-red-600 { color: var(--danger-color); }
.text-green-600 { color: var(--success-color); }
.text-orange-500 { color: #f97316; }
.text-purple-500 { color: var(--primary-color); }

/* 背景颜色类 */
.bg-blue-500 { background-color: var(--info-color); }
.bg-green-500 { background-color: var(--success-color); }
.bg-orange-500 { background-color: #f97316; }
.bg-red-500 { background-color: var(--danger-color); }

/* 尺寸类 */
.w-2 { width: 8px; }
.h-2 { height: 8px; }
.rounded-full { border-radius: 50%; }

/* 文字尺寸 */
.text-xs { font-size: 12px; }

/* Flex 布局工具 */
.flex { display: flex; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-between { justify-content: space-between; }
.gap-3 { gap: 12px; }
.gap-4 { gap: 16px; }
.flex-1 { flex: 1; }

/* 统计卡片 - 参考设计简洁样式 */
.stat-card {
  background: var(--card-bg);
  padding: 24px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal) ease;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.primary {
  background: var(--primary-color);
}
.stat-icon.success {
  background: var(--success-color);
}
.stat-icon.warning {
  background: var(--warning-color);
}
.stat-icon.danger {
  background: var(--danger-color);
}
.stat-icon.info {
  background: var(--info-color);
}

/* 状态指示器 */
.stat-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.stat-status.online {
  color: var(--success-color);
}
.stat-status.warning {
  color: var(--warning-color);
}
.stat-status.offline {
  color: var(--danger-color);
}

.stat-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1.1;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--danger-color);
}

/* 按钮组件 - 简洁扁平设计 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: var(--radius);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  line-height: 1.4;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover {
  background: #16a34a;
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background: #d97706;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.btn-outline:hover {
  background: var(--border-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 状态指示器 */
.status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: var(--radius);
  font-size: 12px;
  font-weight: 500;
  background: var(--border-light);
}

.status.online {
  background: var(--success-bg);
  color: var(--success-color);
}

.status.offline {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.status.warning {
  background: var(--warning-bg);
  color: var(--warning-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* 页面加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 页面元素动画 */
.content {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card {
  animation: scaleIn 0.5s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.card {
  animation: fadeInUp 0.6s ease-out;
}

.nav-item {
  animation: fadeInLeft 0.4s ease-out;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.15s; }
.nav-item:nth-child(3) { animation-delay: 0.2s; }
.nav-item:nth-child(4) { animation-delay: 0.25s; }
.nav-item:nth-child(5) { animation-delay: 0.3s; }
.nav-item:nth-child(6) { animation-delay: 0.35s; }
.nav-item:nth-child(7) { animation-delay: 0.4s; }
.nav-item:nth-child(8) { animation-delay: 0.45s; }

/* 悬停动画增强 */
.card:hover {
  animation: none;
}

.btn:active {
  transform: translateY(-1px) scale(0.98);
}

/* 加载状态动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* 装饰性背景元素 */
.decorative-bg {
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
}

.shape-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  right: 30%;
  animation-delay: 4s;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

/* 玻璃态效果增强 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 微交互效果 */
.interactive-element {
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: translateY(-2px);
}

.interactive-element:active {
  transform: translateY(0px) scale(0.98);
}

/* 数据更新动画 */
@keyframes dataUpdate {
  0% { opacity: 1; }
  50% { opacity: 0.7; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}

.data-updating {
  animation: dataUpdate 0.6s ease-in-out;
}

/* 成功状态动画 */
@keyframes successPulse {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

.success-pulse {
  animation: successPulse 1s ease-out;
}

/* 警告状态动画 */
@keyframes warningShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.warning-shake {
  animation: warningShake 0.5s ease-in-out;
}

/* 加载骨架屏效果 */
@keyframes skeleton {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton 1.5s infinite;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .content {
    padding: 20px 16px;
  }

  .content::before {
    left: 0;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .top-bar {
    padding: 0 16px;
  }

  .user-info {
    display: none;
  }

  /* 统计卡片在小屏幕上堆叠 */
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  /* 页面标题字体大小调整 */
  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  /* 卡片内边距调整 */
  .card-body {
    padding: 20px;
  }

  .card-header {
    padding: 20px 20px 16px 20px;
  }

  .card-title {
    font-size: 18px;
  }

  /* 统计卡片调整 */
  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
  }

  .stat-value {
    font-size: 28px;
  }

  /* 按钮在小屏幕上全宽 */
  .btn-group-mobile {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .btn-group-mobile .btn {
    width: 100%;
  }

  /* 表格横向滚动 */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 12px;
  }

  /* 侧边栏在移动端的调整 */
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  /* 导航项间距调整 */
  .nav-item {
    margin: 2px 12px;
    padding: 12px 16px;
  }

  /* 状态和标签调整 */
  .status, .badge {
    font-size: 11px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .content {
    padding: 16px 12px;
  }

  .grid-cols-4 {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    border-radius: 16px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 24px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  /* 卡片调整 */
  .card {
    border-radius: 16px;
  }

  .card-body {
    padding: 16px;
  }

  .card-header {
    padding: 16px 16px 12px 16px;
  }

  .card-title {
    font-size: 16px;
  }

  /* 按钮调整 */
  .btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  /* 导航栏在超小屏幕上的优化 */
  .breadcrumb {
    font-size: 12px;
  }

  .nav-item {
    padding: 10px 12px;
    margin: 2px 8px;
    font-size: 14px;
  }

  .nav-item i {
    font-size: 16px;
  }

  /* 表格在超小屏幕的优化 */
  .table th,
  .table td {
    padding: 12px 8px;
    font-size: 13px;
  }

  /* 状态和标签进一步调整 */
  .status, .badge {
    font-size: 10px;
    padding: 3px 6px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 图表容器 - 简洁设计 */
.chart-container {
  position: relative;
  height: 300px;
  background: var(--card-bg);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.chart-container .text-center {
  text-align: center;
}

.chart-container i {
  color: var(--primary-color);
  margin-bottom: 12px;
}

/* 数据表格样式 */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-bg);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.data-table th {
  background: var(--border-light);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 13px;
}

.data-table tbody tr:hover {
  background: var(--border-light);
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* 进度条 - 简洁设计 */
.progress {
  width: 100%;
  height: 8px;
  background: var(--border-light);
  border-radius: var(--radius);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
  border-radius: var(--radius);
}

.progress-bar.success {
  background: var(--success-color);
}

.progress-bar.warning {
  background: var(--warning-color);
}

.progress-bar.danger {
  background: var(--danger-color);
}

/* 表格样式 - 现代化设计 */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition-normal) ease;
}

.table th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-weight: 700;
  color: var(--text-primary);
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-gradient);
  opacity: 0.3;
}

.table tbody tr {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.table tbody tr:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 标签样式 - 简洁设计 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--radius);
  font-size: 12px;
  font-weight: 500;
}

.badge.primary {
  background: var(--primary-color);
  color: white;
}

.badge.success {
  background: var(--success-color);
  color: white;
}

.badge.warning {
  background: var(--warning-color);
  color: white;
}

.badge.danger {
  background: var(--danger-color);
  color: white;
}

.badge.secondary {
  background: var(--border-color);
  color: var(--text-secondary);
}

/* 表单验证样式 */
.field-error {
  color: var(--danger-color);
  font-size: 12px;
  margin-top: 4px;
}

.form-input.error,
.form-select.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid var(--primary-light);
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  z-index: 9999;
  animation: slideInRight 0.3s ease-out;
}

.notification.notification-success {
  border-left-color: var(--success-color);
}

.notification.notification-error {
  border-left-color: var(--danger-color);
}

.notification.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.notification-close:hover {
  background-color: #f3f4f6;
}

.notification.fade-out {
  animation: slideOutRight 0.3s ease-out forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: inherit;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--primary-light);
}

.loading-spinner i {
  font-size: 24px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
  animation: scaleIn 0.3s ease-out;
}

.modal-header {
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  padding: 20px 24px;
}

.modal-footer {
  padding: 16px 24px 20px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
