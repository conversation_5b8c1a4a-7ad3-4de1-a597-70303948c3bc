// 智慧楼宇管理系统 - 公共JavaScript

// 导航菜单配置
const navigationConfig = {
  logo: {
    icon: 'fas fa-building',
    text: '智慧楼宇'
  },
  menuGroups: [
    {
      title: '总览',
      items: [
        { id: 'dashboard', icon: 'fas fa-tachometer-alt', text: '仪表板', href: 'index.html' }
      ]
    },
    {
      title: '楼宇控制',
      items: [
        { id: 'bas', icon: 'fas fa-cogs', text: '楼宇自动化', href: 'bas.html' },
        { id: 'energy', icon: 'fas fa-bolt', text: '能源管理', href: 'energy.html' }
      ]
    },
    {
      title: '安全管理',
      items: [
        { id: 'security', icon: 'fas fa-shield-alt', text: '视频监控', href: 'security.html' },
        { id: 'access', icon: 'fas fa-door-open', text: '门禁管理', href: 'access.html' },
        { id: 'elevator', icon: 'fas fa-elevator', text: '梯控管理', href: 'elevator.html' },
        { id: 'visitor', icon: 'fas fa-user-friends', text: '访客管理', href: 'visitor.html' }
      ]
    },
    {
      title: '智能服务',
      items: [
        { id: 'parking', icon: 'fas fa-car', text: '停车管理', href: 'parking.html' },
        { id: 'alarm', icon: 'fas fa-exclamation-triangle', text: '报警管理', href: 'alarm.html' },
        { id: 'meeting', icon: 'fas fa-users', text: '会议系统', href: 'meeting.html' }
      ]
    },
    {
      title: '系统管理',
      items: [
        { id: 'maintenance', icon: 'fas fa-tools', text: '运维管理', href: 'maintenance.html' },
        { id: 'settings', icon: 'fas fa-cog', text: '系统设置', href: 'settings.html' }
      ]
    }
  ]
};

// 生成侧边栏HTML
function generateSidebar() {
  const { logo, menuGroups } = navigationConfig;
  
  let sidebarHTML = `
    <div class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <a href="index.html" class="logo">
          <i class="${logo.icon}"></i>
          <span>${logo.text}</span>
        </a>
      </div>
      <nav class="nav-menu">
  `;
  
  menuGroups.forEach(group => {
    sidebarHTML += `
      <div class="nav-group">
        <div class="nav-group-title">${group.title}</div>
    `;
    
    group.items.forEach(item => {
      const isActive = getCurrentPageId() === item.id ? 'active' : '';
      sidebarHTML += `
        <a href="${item.href}" class="nav-item ${isActive}" data-page="${item.id}">
          <i class="${item.icon}"></i>
          <span>${item.text}</span>
        </a>
      `;
    });
    
    sidebarHTML += `</div>`;
  });
  
  sidebarHTML += `
      </nav>
    </div>
  `;
  
  return sidebarHTML;
}

// 生成顶部栏HTML
function generateTopBar(pageTitle = '', breadcrumbs = []) {
  return `
    <div class="top-bar">
      <div class="breadcrumb">
        <button class="mobile-menu-btn" id="mobileMenuBtn" style="display: none;">
          <i class="fas fa-bars"></i>
        </button>
        <a href="index.html">首页</a>
        ${breadcrumbs.map(crumb => `<i class="fas fa-chevron-right"></i><span>${crumb}</span>`).join('')}
      </div>
      <div class="user-menu">
        <button class="notification-btn">
          <i class="fas fa-bell"></i>
          <span class="notification-badge"></span>
        </button>
        <div class="user-profile">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="user-info">
            <div class="user-name">管理员</div>
            <div class="user-role">系统管理员</div>
          </div>
          <i class="fas fa-chevron-down"></i>
        </div>
      </div>
    </div>
  `;
}

// 获取当前页面ID
function getCurrentPageId() {
  const path = window.location.pathname;
  const filename = path.split('/').pop().replace('.html', '');
  
  if (filename === 'index' || filename === '') return 'dashboard';
  return filename;
}

// 初始化页面
function initializePage(pageTitle = '', breadcrumbs = []) {
  // 生成侧边栏
  const sidebarContainer = document.getElementById('sidebarContainer');
  if (sidebarContainer) {
    sidebarContainer.innerHTML = generateSidebar();
  }
  
  // 生成顶部栏
  const topBarContainer = document.getElementById('topBarContainer');
  if (topBarContainer) {
    topBarContainer.innerHTML = generateTopBar(pageTitle, breadcrumbs);
  }
  
  // 设置页面标题
  if (pageTitle) {
    document.title = `${pageTitle} - 智慧楼宇管理系统`;
  }
  
  // 初始化移动端菜单
  initializeMobileMenu();
  
  // 初始化通知功能
  initializeNotifications();
  
  // 添加页面加载动画
  document.body.classList.add('fade-in');
}

// 移动端菜单控制
function initializeMobileMenu() {
  const mobileMenuBtn = document.getElementById('mobileMenuBtn');
  const sidebar = document.getElementById('sidebar');
  
  if (mobileMenuBtn && sidebar) {
    // 在移动端显示菜单按钮
    if (window.innerWidth <= 1024) {
      mobileMenuBtn.style.display = 'block';
    }
    
    mobileMenuBtn.addEventListener('click', () => {
      sidebar.classList.toggle('open');
    });
    
    // 点击内容区域关闭菜单
    document.addEventListener('click', (e) => {
      if (window.innerWidth <= 1024 && 
          !sidebar.contains(e.target) && 
          !mobileMenuBtn.contains(e.target)) {
        sidebar.classList.remove('open');
      }
    });
  }
  
  // 窗口大小改变时调整菜单
  window.addEventListener('resize', () => {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    if (mobileMenuBtn) {
      if (window.innerWidth <= 1024) {
        mobileMenuBtn.style.display = 'block';
      } else {
        mobileMenuBtn.style.display = 'none';
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
          sidebar.classList.remove('open');
        }
      }
    }
  });
}

// 通知功能
function initializeNotifications() {
  // 模拟通知数据
  const notifications = [
    { id: 1, type: 'warning', message: '空调系统温度异常', time: '2分钟前' },
    { id: 2, type: 'info', message: '访客张三已到达', time: '5分钟前' },
    { id: 3, type: 'danger', message: '停车场车位不足', time: '10分钟前' }
  ];
  
  // 更新通知徽章
  updateNotificationBadge(notifications.length);
}

// 更新通知徽章
function updateNotificationBadge(count) {
  const badge = document.querySelector('.notification-badge');
  if (badge) {
    if (count > 0) {
      badge.style.display = 'block';
    } else {
      badge.style.display = 'none';
    }
  }
}

// 格式化数字
function formatNumber(num) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 格式化时间
function formatTime(date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

// 生成随机数据（用于演示）
function generateRandomData(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 模拟实时数据更新
function simulateRealTimeData() {
  setInterval(() => {
    // 更新页面中的实时数据
    const realTimeElements = document.querySelectorAll('[data-realtime]');
    realTimeElements.forEach(element => {
      const type = element.dataset.realtime;
      switch (type) {
        case 'temperature':
          element.textContent = generateRandomData(20, 28) + '°C';
          break;
        case 'humidity':
          element.textContent = generateRandomData(40, 70) + '%';
          break;
        case 'power':
          element.textContent = generateRandomData(800, 1200) + 'kW';
          break;
        case 'people':
          element.textContent = generateRandomData(150, 300);
          break;
      }
    });
  }, 5000); // 每5秒更新一次
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  // 启动实时数据模拟
  simulateRealTimeData();
});

// 表单验证功能
function validateForm(form) {
  const requiredFields = form.querySelectorAll('[required]');
  let isValid = true;

  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      showFieldError(field, '此字段为必填项');
      isValid = false;
    } else {
      clearFieldError(field);
    }
  });

  // 邮箱验证
  const emailFields = form.querySelectorAll('input[type="email"]');
  emailFields.forEach(field => {
    if (field.value && !isValidEmail(field.value)) {
      showFieldError(field, '请输入有效的邮箱地址');
      isValid = false;
    }
  });

  // 电话验证
  const phoneFields = form.querySelectorAll('input[type="tel"]');
  phoneFields.forEach(field => {
    if (field.value && !isValidPhone(field.value)) {
      showFieldError(field, '请输入有效的电话号码');
      isValid = false;
    }
  });

  return isValid;
}

function showFieldError(field, message) {
  clearFieldError(field);

  const errorDiv = document.createElement('div');
  errorDiv.className = 'field-error';
  errorDiv.textContent = message;

  field.parentNode.appendChild(errorDiv);
  field.classList.add('error');
}

function clearFieldError(field) {
  const existingError = field.parentNode.querySelector('.field-error');
  if (existingError) {
    existingError.remove();
  }
  field.classList.remove('error');
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidPhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
}

// 通知系统
function showNotification(message, type = 'info', duration = 3000) {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas fa-${getNotificationIcon(type)}"></i>
      <span>${message}</span>
    </div>
    <button class="notification-close">
      <i class="fas fa-times"></i>
    </button>
  `;

  document.body.appendChild(notification);

  // 自动关闭
  setTimeout(() => {
    notification.classList.add('fade-out');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, duration);

  // 手动关闭
  notification.querySelector('.notification-close').addEventListener('click', () => {
    notification.classList.add('fade-out');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  });
}

function getNotificationIcon(type) {
  const icons = {
    success: 'check-circle',
    error: 'exclamation-circle',
    warning: 'exclamation-triangle',
    info: 'info-circle'
  };
  return icons[type] || 'info-circle';
}

// 加载状态管理
function showLoading(element, text = '加载中...') {
  const loadingDiv = document.createElement('div');
  loadingDiv.className = 'loading-overlay';
  loadingDiv.innerHTML = `
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <span>${text}</span>
    </div>
  `;

  element.style.position = 'relative';
  element.appendChild(loadingDiv);
}

function hideLoading(element) {
  const loadingOverlay = element.querySelector('.loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.remove();
  }
}

// 确认对话框
function showConfirm(message, onConfirm, onCancel) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>确认操作</h3>
      </div>
      <div class="modal-body">
        <p>${message}</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline modal-cancel">取消</button>
        <button class="btn btn-primary modal-confirm">确认</button>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  modal.querySelector('.modal-confirm').addEventListener('click', () => {
    modal.remove();
    if (onConfirm) onConfirm();
  });

  modal.querySelector('.modal-cancel').addEventListener('click', () => {
    modal.remove();
    if (onCancel) onCancel();
  });

  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
      if (onCancel) onCancel();
    }
  });
}

// 添加交互效果
function addInteractiveEffects() {
  // 为所有卡片添加交互效果
  document.querySelectorAll('.card, .stat-card').forEach(element => {
    element.classList.add('interactive-element');
  });

  // 为按钮添加点击效果
  document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('click', function() {
      this.classList.add('success-pulse');
      setTimeout(() => {
        this.classList.remove('success-pulse');
      }, 1000);
    });
  });

  // 数据更新动画
  setInterval(() => {
    const dataElements = document.querySelectorAll('[data-realtime]');
    dataElements.forEach(element => {
      if (Math.random() > 0.7) { // 30% 概率触发更新动画
        element.classList.add('data-updating');
        setTimeout(() => {
          element.classList.remove('data-updating');
        }, 600);
      }
    });
  }, 5000);
}

// 页面加载完成后添加交互效果
document.addEventListener('DOMContentLoaded', () => {
  addInteractiveEffects();

  // 添加页面切换动画
  const content = document.querySelector('.content');
  if (content) {
    content.style.opacity = '0';
    content.style.transform = 'translateY(20px)';

    setTimeout(() => {
      content.style.transition = 'all 0.6s ease-out';
      content.style.opacity = '1';
      content.style.transform = 'translateY(0)';
    }, 100);
  }
});

// 导出公共函数
window.SmartBuilding = {
  initializePage,
  formatNumber,
  formatTime,
  generateRandomData,
  updateNotificationBadge,
  validateForm,
  showNotification,
  showLoading,
  hideLoading,
  showConfirm,
  addInteractiveEffects
};
