<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">会议系统</h1>
                    <p class="page-subtitle">智能会议预约、设备控制、环境调节一体化管理</p>
                </div>
                
                <!-- 会议概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value">8</div>
                        <div class="stat-label">会议室总数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>全部可用</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="stat-value">5</div>
                        <div class="stat-label">进行中会议</div>
                        <div class="stat-change positive">
                            <i class="fas fa-clock"></i>
                            <span>实时统计</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                        </div>
                        <div class="stat-value">12</div>
                        <div class="stat-label">今日预约</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>较昨日 +3</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-percentage"></i>
                            </div>
                        </div>
                        <div class="stat-value">75%</div>
                        <div class="stat-label">使用率</div>
                        <div class="stat-change positive">
                            <i class="fas fa-chart-line"></i>
                            <span>高效利用</span>
                        </div>
                    </div>
                </div>
                
                <!-- 会议室状态和快速预约 -->
                <div class="grid grid-cols-3 gap-6 mb-6">
                    <!-- 会议室状态 -->
                    <div class="card col-span-2">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-door-open mr-2"></i>
                                会议室状态
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-sync"></i>
                                    刷新
                                </button>
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i>
                                    新建预约
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="meeting-rooms-grid">
                                <div class="meeting-room available" data-room="A01">
                                    <div class="room-header">
                                        <div class="room-name">会议室 A01</div>
                                        <span class="room-status available">
                                            <span class="status-dot"></span>
                                            空闲
                                        </span>
                                    </div>
                                    <div class="room-info">
                                        <div class="room-capacity">
                                            <i class="fas fa-users"></i>
                                            <span>10人</span>
                                        </div>
                                        <div class="room-equipment">
                                            <i class="fas fa-tv"></i>
                                            <i class="fas fa-microphone"></i>
                                            <i class="fas fa-wifi"></i>
                                        </div>
                                    </div>
                                    <div class="room-schedule">
                                        <div class="next-meeting">下次会议: 15:00-16:00</div>
                                    </div>
                                    <button class="btn btn-primary btn-sm w-full">立即预约</button>
                                </div>
                                
                                <div class="meeting-room occupied" data-room="A02">
                                    <div class="room-header">
                                        <div class="room-name">会议室 A02</div>
                                        <span class="room-status occupied">
                                            <span class="status-dot"></span>
                                            使用中
                                        </span>
                                    </div>
                                    <div class="room-info">
                                        <div class="room-capacity">
                                            <i class="fas fa-users"></i>
                                            <span>6人</span>
                                        </div>
                                        <div class="room-equipment">
                                            <i class="fas fa-tv"></i>
                                            <i class="fas fa-microphone"></i>
                                        </div>
                                    </div>
                                    <div class="room-schedule">
                                        <div class="current-meeting">当前: 技术评审会议</div>
                                        <div class="meeting-time">14:00-15:30 (剩余45分钟)</div>
                                    </div>
                                    <button class="btn btn-outline btn-sm w-full" disabled>使用中</button>
                                </div>
                                
                                <div class="meeting-room available" data-room="B01">
                                    <div class="room-header">
                                        <div class="room-name">会议室 B01</div>
                                        <span class="room-status available">
                                            <span class="status-dot"></span>
                                            空闲
                                        </span>
                                    </div>
                                    <div class="room-info">
                                        <div class="room-capacity">
                                            <i class="fas fa-users"></i>
                                            <span>20人</span>
                                        </div>
                                        <div class="room-equipment">
                                            <i class="fas fa-tv"></i>
                                            <i class="fas fa-microphone"></i>
                                            <i class="fas fa-wifi"></i>
                                            <i class="fas fa-video"></i>
                                        </div>
                                    </div>
                                    <div class="room-schedule">
                                        <div class="next-meeting">下次会议: 16:30-18:00</div>
                                    </div>
                                    <button class="btn btn-primary btn-sm w-full">立即预约</button>
                                </div>
                                
                                <div class="meeting-room occupied" data-room="B02">
                                    <div class="room-header">
                                        <div class="room-name">会议室 B02</div>
                                        <span class="room-status occupied">
                                            <span class="status-dot"></span>
                                            使用中
                                        </span>
                                    </div>
                                    <div class="room-info">
                                        <div class="room-capacity">
                                            <i class="fas fa-users"></i>
                                            <span>8人</span>
                                        </div>
                                        <div class="room-equipment">
                                            <i class="fas fa-tv"></i>
                                            <i class="fas fa-wifi"></i>
                                        </div>
                                    </div>
                                    <div class="room-schedule">
                                        <div class="current-meeting">当前: 项目讨论</div>
                                        <div class="meeting-time">13:30-15:00 (剩余15分钟)</div>
                                    </div>
                                    <button class="btn btn-outline btn-sm w-full" disabled>使用中</button>
                                </div>
                                
                                <div class="meeting-room maintenance" data-room="C01">
                                    <div class="room-header">
                                        <div class="room-name">会议室 C01</div>
                                        <span class="room-status maintenance">
                                            <span class="status-dot"></span>
                                            维护中
                                        </span>
                                    </div>
                                    <div class="room-info">
                                        <div class="room-capacity">
                                            <i class="fas fa-users"></i>
                                            <span>15人</span>
                                        </div>
                                        <div class="room-equipment">
                                            <i class="fas fa-tv"></i>
                                            <i class="fas fa-microphone"></i>
                                            <i class="fas fa-wifi"></i>
                                        </div>
                                    </div>
                                    <div class="room-schedule">
                                        <div class="maintenance-info">设备维护中，预计明日恢复</div>
                                    </div>
                                    <button class="btn btn-outline btn-sm w-full" disabled>维护中</button>
                                </div>
                                
                                <div class="meeting-room available" data-room="C02">
                                    <div class="room-header">
                                        <div class="room-name">会议室 C02</div>
                                        <span class="room-status available">
                                            <span class="status-dot"></span>
                                            空闲
                                        </span>
                                    </div>
                                    <div class="room-info">
                                        <div class="room-capacity">
                                            <i class="fas fa-users"></i>
                                            <span>4人</span>
                                        </div>
                                        <div class="room-equipment">
                                            <i class="fas fa-tv"></i>
                                            <i class="fas fa-wifi"></i>
                                        </div>
                                    </div>
                                    <div class="room-schedule">
                                        <div class="next-meeting">全天空闲</div>
                                    </div>
                                    <button class="btn btn-primary btn-sm w-full">立即预约</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 快速预约 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-calendar-plus mr-2"></i>
                                快速预约
                            </h3>
                        </div>
                        <div class="card-body">
                            <form class="space-y-4">
                                <div>
                                    <label class="form-label">会议主题</label>
                                    <input type="text" class="form-input" placeholder="请输入会议主题" required>
                                </div>
                                <div>
                                    <label class="form-label">会议室</label>
                                    <select class="form-select" required>
                                        <option value="">请选择会议室</option>
                                        <option value="A01">会议室 A01 (10人)</option>
                                        <option value="B01">会议室 B01 (20人)</option>
                                        <option value="C02">会议室 C02 (4人)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="form-label">开始时间</label>
                                    <input type="datetime-local" class="form-input" required>
                                </div>
                                <div>
                                    <label class="form-label">结束时间</label>
                                    <input type="datetime-local" class="form-input" required>
                                </div>
                                <div>
                                    <label class="form-label">参会人员</label>
                                    <textarea class="form-input" rows="3" placeholder="请输入参会人员邮箱，用逗号分隔"></textarea>
                                </div>
                                <div>
                                    <label class="form-label">设备需求</label>
                                    <div class="equipment-options">
                                        <label class="checkbox-item">
                                            <input type="checkbox" checked>
                                            <span>投影设备</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox">
                                            <span>音响设备</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" checked>
                                            <span>视频会议</span>
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox">
                                            <span>白板</span>
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary w-full">
                                    <i class="fas fa-check"></i>
                                    立即预约
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 今日会议安排和设备控制 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 今日会议安排 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-calendar-day mr-2"></i>
                                今日会议安排
                            </h3>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-calendar"></i>
                                查看日历
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="meeting-schedule-item ongoing">
                                    <div class="meeting-time-badge ongoing">进行中</div>
                                    <div class="meeting-details">
                                        <div class="meeting-title">技术评审会议</div>
                                        <div class="meeting-meta">
                                            <span><i class="fas fa-clock"></i> 14:00-15:30</span>
                                            <span><i class="fas fa-door-open"></i> 会议室 A02</span>
                                            <span><i class="fas fa-users"></i> 6人</span>
                                        </div>
                                        <div class="meeting-organizer">组织者: 张三</div>
                                    </div>
                                    <button class="btn btn-outline btn-sm">详情</button>
                                </div>
                                
                                <div class="meeting-schedule-item upcoming">
                                    <div class="meeting-time-badge upcoming">即将开始</div>
                                    <div class="meeting-details">
                                        <div class="meeting-title">产品规划讨论</div>
                                        <div class="meeting-meta">
                                            <span><i class="fas fa-clock"></i> 15:00-16:00</span>
                                            <span><i class="fas fa-door-open"></i> 会议室 A01</span>
                                            <span><i class="fas fa-users"></i> 8人</span>
                                        </div>
                                        <div class="meeting-organizer">组织者: 李四</div>
                                    </div>
                                    <button class="btn btn-primary btn-sm">加入</button>
                                </div>
                                
                                <div class="meeting-schedule-item scheduled">
                                    <div class="meeting-time-badge scheduled">已安排</div>
                                    <div class="meeting-details">
                                        <div class="meeting-title">客户演示</div>
                                        <div class="meeting-meta">
                                            <span><i class="fas fa-clock"></i> 16:30-18:00</span>
                                            <span><i class="fas fa-door-open"></i> 会议室 B01</span>
                                            <span><i class="fas fa-users"></i> 12人</span>
                                        </div>
                                        <div class="meeting-organizer">组织者: 王五</div>
                                    </div>
                                    <button class="btn btn-outline btn-sm">详情</button>
                                </div>
                                
                                <div class="meeting-schedule-item scheduled">
                                    <div class="meeting-time-badge scheduled">已安排</div>
                                    <div class="meeting-details">
                                        <div class="meeting-title">周例会</div>
                                        <div class="meeting-meta">
                                            <span><i class="fas fa-clock"></i> 19:00-20:00</span>
                                            <span><i class="fas fa-door-open"></i> 会议室 C02</span>
                                            <span><i class="fas fa-users"></i> 4人</span>
                                        </div>
                                        <div class="meeting-organizer">组织者: 赵六</div>
                                    </div>
                                    <button class="btn btn-outline btn-sm">详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 设备控制 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-cogs mr-2"></i>
                                设备控制
                            </h3>
                            <select class="form-select" style="width: auto;" id="roomSelect">
                                <option value="A02">会议室 A02</option>
                                <option value="A01">会议室 A01</option>
                                <option value="B01">会议室 B01</option>
                                <option value="C02">会议室 C02</option>
                            </select>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 环境控制 -->
                                <div>
                                    <h4 class="font-medium mb-2">环境控制</h4>
                                    <div class="space-y-3">
                                        <div class="control-item">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-lightbulb text-yellow-500"></i>
                                                    <span>照明</span>
                                                </div>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="control-slider">
                                                <input type="range" min="0" max="100" value="80" class="slider-range">
                                                <span class="slider-value">80%</span>
                                            </div>
                                        </div>
                                        
                                        <div class="control-item">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-snowflake text-blue-500"></i>
                                                    <span>空调</span>
                                                </div>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="control-temp">
                                                <button class="btn btn-outline btn-xs">-</button>
                                                <span class="temp-display">24°C</span>
                                                <button class="btn btn-outline btn-xs">+</button>
                                            </div>
                                        </div>
                                        
                                        <div class="control-item">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-volume-up text-green-500"></i>
                                                    <span>音响</span>
                                                </div>
                                                <label class="switch">
                                                    <input type="checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 多媒体设备 -->
                                <div>
                                    <h4 class="font-medium mb-2">多媒体设备</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-tv text-purple-500"></i>
                                                <span>投影仪</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-microphone text-red-500"></i>
                                                <span>麦克风</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-video text-blue-500"></i>
                                                <span>摄像头</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox">
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 快捷操作 -->
                                <div>
                                    <h4 class="font-medium mb-2">快捷操作</h4>
                                    <div class="grid grid-cols-2 gap-2">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-play"></i>
                                            开始会议
                                        </button>
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-pause"></i>
                                            暂停会议
                                        </button>
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-stop"></i>
                                            结束会议
                                        </button>
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-power-off"></i>
                                            关闭设备
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化会议系统页面
        SmartBuilding.initializePage('会议系统', ['智能服务', '会议系统']);
        
        // 表单提交处理
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('会议预约成功！');
            this.reset();
        });
        
        // 会议室预约按钮
        document.querySelectorAll('.meeting-room .btn-primary').forEach(btn => {
            btn.addEventListener('click', function() {
                const roomName = this.closest('.meeting-room').querySelector('.room-name').textContent;
                alert(`预约 ${roomName} 成功！`);
            });
        });
        
        // 滑块控制
        document.querySelectorAll('.slider-range').forEach(slider => {
            slider.addEventListener('input', function() {
                const value = this.value;
                const valueDisplay = this.nextElementSibling;
                if (valueDisplay) {
                    valueDisplay.textContent = value + '%';
                }
            });
        });
        
        // 温度控制
        document.querySelectorAll('.control-temp .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tempDisplay = this.parentElement.querySelector('.temp-display');
                let currentTemp = parseInt(tempDisplay.textContent);
                
                if (this.textContent === '+') {
                    currentTemp = Math.min(currentTemp + 1, 30);
                } else {
                    currentTemp = Math.max(currentTemp - 1, 16);
                }
                
                tempDisplay.textContent = currentTemp + '°C';
            });
        });
    </script>

    <style>
        /* 会议室网格布局 */
        .meeting-rooms-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        /* 会议室卡片样式 */
        .meeting-room {
            background: white;
            border-radius: 12px;
            padding: 16px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .meeting-room:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .meeting-room.available {
            border-color: var(--success-color);
        }

        .meeting-room.occupied {
            border-color: var(--danger-color);
            background: #fef2f2;
        }

        .meeting-room.maintenance {
            border-color: var(--warning-color);
            background: #fffbeb;
        }

        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .room-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
        }

        .room-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .room-status.available {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .room-status.occupied {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .room-status.maintenance {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .room-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .room-capacity {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .room-equipment {
            display: flex;
            gap: 8px;
            color: var(--text-secondary);
        }

        .room-equipment i {
            font-size: 14px;
        }

        .room-schedule {
            margin-bottom: 12px;
            font-size: 13px;
        }

        .next-meeting,
        .current-meeting,
        .maintenance-info {
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .current-meeting {
            color: var(--danger-color);
            font-weight: 500;
        }

        .meeting-time {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* 表单样式 */
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .form-input,
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .equipment-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            cursor: pointer;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        /* 会议安排项样式 */
        .meeting-schedule-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid transparent;
        }

        .meeting-schedule-item.ongoing {
            border-left-color: var(--danger-color);
            background: #fef2f2;
        }

        .meeting-schedule-item.upcoming {
            border-left-color: var(--warning-color);
            background: #fffbeb;
        }

        .meeting-schedule-item.scheduled {
            border-left-color: var(--primary-light);
        }

        .meeting-time-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            white-space: nowrap;
        }

        .meeting-time-badge.ongoing {
            background: var(--danger-color);
            color: white;
        }

        .meeting-time-badge.upcoming {
            background: var(--warning-color);
            color: white;
        }

        .meeting-time-badge.scheduled {
            background: var(--primary-light);
            color: white;
        }

        .meeting-details {
            flex: 1;
        }

        .meeting-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 6px;
        }

        .meeting-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 4px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .meeting-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .meeting-organizer {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 设备控制样式 */
        .control-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .control-slider {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 8px;
        }

        .slider-range {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-light);
            cursor: pointer;
        }

        .slider-value {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-primary);
            min-width: 35px;
        }

        .control-temp {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 8px;
            justify-content: center;
        }

        .temp-display {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            min-width: 50px;
            text-align: center;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-light);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .btn-xs {
            padding: 4px 8px;
            font-size: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .w-full {
            width: 100%;
        }

        .col-span-2 {
            grid-column: span 2;
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }

        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }

        .flex {
            display: flex;
        }

        .flex-wrap {
            flex-wrap: wrap;
        }

        .items-center {
            align-items: center;
        }

        .items-start {
            align-items: flex-start;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .gap-6 {
            gap: 1.5rem;
        }

        .gap-8 {
            gap: 2rem;
        }

        .gap-12 {
            gap: 3rem;
        }

        .flex-1 {
            flex: 1;
        }

        @media (max-width: 1024px) {
            .meeting-rooms-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .meeting-rooms-grid {
                grid-template-columns: 1fr;
            }

            .equipment-options {
                grid-template-columns: 1fr;
            }

            .meeting-meta {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</body>
</html>
