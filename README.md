# 智慧楼宇管理系统

一个现代化的智慧楼宇管理系统前端界面，提供楼宇自动化、能源管理、安全监控、访客管理等全方位的楼宇管理功能。

## 🏢 系统概述

智慧楼宇管理系统是一个集成化的楼宇管理平台，通过现代化的Web界面提供以下核心功能：

- **楼宇自动化系统 (BAS)** - 空调、照明、通风、电梯等设备的智能控制
- **能源管理系统 (EMS)** - 电力、水、气等能源的实时监控和分析
- **视频监控系统 (VMS)** - 实时视频监控和AI智能分析
- **门禁管理系统** - 多种认证方式的门禁控制和权限管理
- **访客管理系统** - 访客预约、登记、通行管理
- **停车管理系统** - 智能停车引导和收费管理
- **会议系统** - 会议室预约和设备控制
- **运维管理系统** - 设备维护、工单管理、巡检计划
- **系统设置** - 用户管理、安全设置、系统配置

## 🚀 技术特点

- **现代化设计** - 采用现代化的UI设计语言，界面简洁美观
- **响应式布局** - 支持桌面端、平板和移动设备
- **模块化架构** - 清晰的代码结构，易于维护和扩展
- **实时数据** - 模拟实时数据更新，提供动态的用户体验
- **交互丰富** - 完善的表单验证、通知系统、确认对话框等交互功能

## 📁 项目结构

```
智慧楼宇管理系统/
├── index.html              # 主页面 - 系统仪表板
├── bas.html                # 楼宇自动化系统
├── energy.html             # 能源管理系统
├── security.html           # 视频监控系统
├── access.html             # 门禁管理系统
├── visitor.html            # 访客管理系统
├── parking.html            # 停车管理系统
├── meeting.html            # 会议系统
├── maintenance.html        # 运维管理系统
├── settings.html           # 系统设置
├── assets/
│   ├── css/
│   │   └── style.css       # 全局样式文件
│   └── js/
│       └── common.js       # 公共JavaScript函数
└── README.md               # 项目说明文档
```

## 🎨 设计系统

### 色彩规范
- **主色调**: 深蓝色 (#1e3a8a) + 科技蓝 (#3b82f6)
- **成功色**: 绿色 (#10b981)
- **警告色**: 橙色 (#f59e0b)
- **危险色**: 红色 (#ef4444)
- **文字色**: 深灰 (#1f2937) + 中灰 (#6b7280)

### 布局规范
- **侧边栏**: 固定宽度 280px，深色主题
- **顶部栏**: 高度 64px，包含面包屑和用户信息
- **内容区**: 响应式网格布局，卡片式设计
- **间距**: 8px 基础单位，16px、24px、32px 递增

## 🛠️ 快速开始

### 环境要求
- 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- 本地Web服务器 (可选，用于开发调试)

### 安装运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 智慧楼宇管理系统
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python
   python3 -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 或使用PHP
   php -S localhost:8000
   ```

3. **访问系统**
   打开浏览器访问 `http://localhost:8000`

### 直接使用
也可以直接双击 `index.html` 文件在浏览器中打开，但建议使用本地服务器以获得最佳体验。

## 📱 功能模块

### 1. 系统仪表板 (index.html)
- 系统概览统计
- 实时数据监控
- 快速操作入口
- 最新动态展示

### 2. 楼宇自动化 (bas.html)
- 空调系统控制
- 智能照明管理
- 通风系统监控
- 电梯运行状态

### 3. 能源管理 (energy.html)
- 能耗实时监控
- 分项计量分析
- 楼层能耗统计
- 设备能效监控

### 4. 安全监控 (security.html)
- 视频监控画面
- AI智能分析
- 告警记录管理
- 摄像头状态监控

### 5. 门禁管理 (access.html)
- 门禁设备控制
- 权限管理
- 通行记录查询
- 异常告警处理

### 6. 访客管理 (visitor.html)
- 快速访客登记
- 预约审批流程
- 在访访客管理
- 访客统计分析

### 7. 停车管理 (parking.html)
- 停车场地图
- 车位状态监控
- 车辆管理
- 收费统计

### 8. 会议系统 (meeting.html)
- 会议室状态
- 快速预约
- 设备控制
- 会议安排

### 9. 运维管理 (maintenance.html)
- 工单管理
- 设备状态监控
- 巡检计划
- 维护记录

### 10. 系统设置 (settings.html)
- 常规设置
- 用户管理
- 安全设置
- 系统信息

## 🔧 自定义配置

### 修改导航菜单
编辑 `assets/js/common.js` 文件中的 `navigationConfig` 对象：

```javascript
const navigationConfig = {
  logo: {
    icon: 'fas fa-building',
    text: '智慧楼宇'
  },
  menuGroups: [
    // 添加或修改菜单项
  ]
};
```

### 修改主题色彩
编辑 `assets/css/style.css` 文件中的 CSS 变量：

```css
:root {
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  /* 修改其他颜色变量 */
}
```

### 添加新页面
1. 创建新的 HTML 文件
2. 在 `navigationConfig` 中添加菜单项
3. 确保页面包含必要的容器元素

## 📊 数据模拟

系统包含丰富的模拟数据功能：

- **实时数据更新** - 温度、功耗、人数等数据每5-10秒自动更新
- **状态变化模拟** - 设备状态、车位占用等状态随机变化
- **交互反馈** - 按钮点击、表单提交等操作提供即时反馈

## 🌐 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |
| IE | - | ❌ 不支持 |

## 📝 开发说明

### 代码规范
- 使用语义化的HTML标签
- CSS采用BEM命名规范
- JavaScript使用ES6+语法
- 注释清晰，代码可读性强

### 扩展建议
- 可以集成真实的后端API
- 添加数据持久化功能
- 集成第三方图表库
- 添加更多的动画效果

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**智慧楼宇管理系统** - 让楼宇管理更智能、更高效！
