<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车管理系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>

            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">停车管理系统</h1>
                    <p class="page-subtitle">智能停车引导，车牌识别，无感支付一体化管理</p>
                </div>

                <!-- 停车概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-car"></i>
                            </div>
                        </div>
                        <div class="stat-value">156/200</div>
                        <div class="stat-label">车位占用</div>
                        <div class="stat-change negative">
                            <i class="fas fa-percentage"></i>
                            <span>78% 占用率</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-parking"></i>
                            </div>
                        </div>
                        <div class="stat-value">44</div>
                        <div class="stat-label">空闲车位</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>可用</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="stat-value">23</div>
                        <div class="stat-label">今日进出</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>较昨日 +5</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value">3</div>
                        <div class="stat-label">异常车辆</div>
                        <div class="stat-change negative">
                            <i class="fas fa-shield-alt"></i>
                            <span>需要处理</span>
                        </div>
                    </div>
                </div>

                <!-- 停车场地图和实时状态 -->
                <div class="grid grid-cols-3 gap-6 mb-6">
                    <!-- 停车场地图 -->
                    <div class="card col-span-2">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-map mr-2"></i>
                                停车场地图
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-expand"></i>
                                    全屏
                                </button>
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-sync"></i>
                                    刷新
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <!-- 停车场布局 -->
                            <div class="parking-layout">
                                <!-- 入口 -->
                                <div class="parking-entrance">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>入口</span>
                                </div>

                                <!-- A区 -->
                                <div class="parking-zone">
                                    <div class="zone-title">A区 (20/25)</div>
                                    <div class="parking-spots">
                                        <div class="parking-spot occupied" data-spot="A01">A01</div>
                                        <div class="parking-spot occupied" data-spot="A02">A02</div>
                                        <div class="parking-spot available" data-spot="A03">A03</div>
                                        <div class="parking-spot occupied" data-spot="A04">A04</div>
                                        <div class="parking-spot available" data-spot="A05">A05</div>
                                        <div class="parking-spot occupied" data-spot="A06">A06</div>
                                        <div class="parking-spot occupied" data-spot="A07">A07</div>
                                        <div class="parking-spot available" data-spot="A08">A08</div>
                                        <div class="parking-spot occupied" data-spot="A09">A09</div>
                                        <div class="parking-spot occupied" data-spot="A10">A10</div>
                                        <div class="parking-spot occupied" data-spot="A11">A11</div>
                                        <div class="parking-spot available" data-spot="A12">A12</div>
                                        <div class="parking-spot occupied" data-spot="A13">A13</div>
                                        <div class="parking-spot occupied" data-spot="A14">A14</div>
                                        <div class="parking-spot available" data-spot="A15">A15</div>
                                        <div class="parking-spot occupied" data-spot="A16">A16</div>
                                        <div class="parking-spot occupied" data-spot="A17">A17</div>
                                        <div class="parking-spot occupied" data-spot="A18">A18</div>
                                        <div class="parking-spot occupied" data-spot="A19">A19</div>
                                        <div class="parking-spot occupied" data-spot="A20">A20</div>
                                        <div class="parking-spot occupied" data-spot="A21">A21</div>
                                        <div class="parking-spot occupied" data-spot="A22">A22</div>
                                        <div class="parking-spot occupied" data-spot="A23">A23</div>
                                        <div class="parking-spot occupied" data-spot="A24">A24</div>
                                        <div class="parking-spot occupied" data-spot="A25">A25</div>
                                    </div>
                                </div>

                                <!-- B区 -->
                                <div class="parking-zone">
                                    <div class="zone-title">B区 (18/25)</div>
                                    <div class="parking-spots">
                                        <div class="parking-spot occupied" data-spot="B01">B01</div>
                                        <div class="parking-spot available" data-spot="B02">B02</div>
                                        <div class="parking-spot occupied" data-spot="B03">B03</div>
                                        <div class="parking-spot available" data-spot="B04">B04</div>
                                        <div class="parking-spot occupied" data-spot="B05">B05</div>
                                        <div class="parking-spot available" data-spot="B06">B06</div>
                                        <div class="parking-spot occupied" data-spot="B07">B07</div>
                                        <div class="parking-spot available" data-spot="B08">B08</div>
                                        <div class="parking-spot occupied" data-spot="B09">B09</div>
                                        <div class="parking-spot available" data-spot="B10">B10</div>
                                        <div class="parking-spot occupied" data-spot="B11">B11</div>
                                        <div class="parking-spot available" data-spot="B12">B12</div>
                                        <div class="parking-spot occupied" data-spot="B13">B13</div>
                                        <div class="parking-spot available" data-spot="B14">B14</div>
                                        <div class="parking-spot occupied" data-spot="B15">B15</div>
                                        <div class="parking-spot occupied" data-spot="B16">B16</div>
                                        <div class="parking-spot occupied" data-spot="B17">B17</div>
                                        <div class="parking-spot occupied" data-spot="B18">B18</div>
                                        <div class="parking-spot occupied" data-spot="B19">B19</div>
                                        <div class="parking-spot occupied" data-spot="B20">B20</div>
                                        <div class="parking-spot occupied" data-spot="B21">B21</div>
                                        <div class="parking-spot occupied" data-spot="B22">B22</div>
                                        <div class="parking-spot occupied" data-spot="B23">B23</div>
                                        <div class="parking-spot occupied" data-spot="B24">B24</div>
                                        <div class="parking-spot occupied" data-spot="B25">B25</div>
                                    </div>
                                </div>

                                <!-- 出口 -->
                                <div class="parking-exit">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>出口</span>
                                </div>
                            </div>

                            <!-- 图例 -->
                            <div class="parking-legend">
                                <div class="legend-item">
                                    <div class="legend-color occupied"></div>
                                    <span>已占用</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color available"></div>
                                    <span>空闲</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color reserved"></div>
                                    <span>预留</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color disabled"></div>
                                    <span>维护</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时监控 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-eye mr-2"></i>
                                实时监控
                            </h3>
                            <span class="badge success">在线</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 入口监控 -->
                                <div>
                                    <h4 class="font-medium mb-2">入口监控</h4>
                                    <div class="monitor-panel">
                                        <div class="monitor-screen">
                                            <i class="fas fa-video text-2xl"></i>
                                            <p class="text-sm mt-2">入口摄像头</p>
                                        </div>
                                        <div class="monitor-info">
                                            <div class="text-sm">
                                                <span class="text-gray-500">最近车辆:</span>
                                                <span class="font-medium">京A12345</span>
                                            </div>
                                            <div class="text-sm">
                                                <span class="text-gray-500">进入时间:</span>
                                                <span class="font-medium">14:23</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 出口监控 -->
                                <div>
                                    <h4 class="font-medium mb-2">出口监控</h4>
                                    <div class="monitor-panel">
                                        <div class="monitor-screen">
                                            <i class="fas fa-video text-2xl"></i>
                                            <p class="text-sm mt-2">出口摄像头</p>
                                        </div>
                                        <div class="monitor-info">
                                            <div class="text-sm">
                                                <span class="text-gray-500">最近车辆:</span>
                                                <span class="font-medium">京B67890</span>
                                            </div>
                                            <div class="text-sm">
                                                <span class="text-gray-500">离开时间:</span>
                                                <span class="font-medium">14:15</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 设备状态 -->
                                <div>
                                    <h4 class="font-medium mb-2">设备状态</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">入口道闸</span>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">出口道闸</span>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">车牌识别</span>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">引导屏</span>
                                            <span class="status warning">
                                                <span class="status-dot"></span>
                                                维护
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 车辆管理和收费统计 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 车辆管理 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list mr-2"></i>
                                车辆管理
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i>
                                    添加车辆
                                </button>
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-search"></i>
                                    查询
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="vehicle-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="vehicle-icon">
                                                <i class="fas fa-car"></i>
                                            </div>
                                            <div>
                                                <div class="vehicle-plate">京A12345</div>
                                                <div class="vehicle-info">月卡用户 · A区A01</div>
                                                <div class="vehicle-time">进入时间: 08:30</div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge success">在场</span>
                                            <button class="btn btn-outline btn-xs">详情</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="vehicle-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="vehicle-icon">
                                                <i class="fas fa-car"></i>
                                            </div>
                                            <div>
                                                <div class="vehicle-plate">京B67890</div>
                                                <div class="vehicle-info">临时用户 · B区B05</div>
                                                <div class="vehicle-time">进入时间: 10:15</div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge warning">临停</span>
                                            <button class="btn btn-outline btn-xs">详情</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="vehicle-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="vehicle-icon">
                                                <i class="fas fa-car"></i>
                                            </div>
                                            <div>
                                                <div class="vehicle-plate">京C11111</div>
                                                <div class="vehicle-info">访客车辆 · A区A08</div>
                                                <div class="vehicle-time">进入时间: 14:00</div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge primary">访客</span>
                                            <button class="btn btn-outline btn-xs">详情</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="vehicle-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="vehicle-icon warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </div>
                                            <div>
                                                <div class="vehicle-plate">京D22222</div>
                                                <div class="vehicle-info">异常车辆 · 超时停放</div>
                                                <div class="vehicle-time">进入时间: 昨天 18:30</div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge danger">异常</span>
                                            <button class="btn btn-warning btn-xs">处理</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="vehicle-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="vehicle-icon">
                                                <i class="fas fa-car"></i>
                                            </div>
                                            <div>
                                                <div class="vehicle-plate">京E33333</div>
                                                <div class="vehicle-info">月卡用户 · B区B12</div>
                                                <div class="vehicle-time">进入时间: 09:45</div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="badge success">在场</span>
                                            <button class="btn btn-outline btn-xs">详情</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收费统计 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-money-bill mr-2"></i>
                                收费统计
                            </h3>
                            <select class="form-select" style="width: auto;">
                                <option>今日</option>
                                <option>本周</option>
                                <option>本月</option>
                            </select>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 收入统计 -->
                                <div>
                                    <h4 class="font-medium mb-2">收入统计</h4>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="stat-mini">
                                            <div class="stat-mini-value">¥1,280</div>
                                            <div class="stat-mini-label">今日收入</div>
                                        </div>
                                        <div class="stat-mini">
                                            <div class="stat-mini-value">¥8,960</div>
                                            <div class="stat-mini-label">本周收入</div>
                                        </div>
                                        <div class="stat-mini">
                                            <div class="stat-mini-value">¥35,200</div>
                                            <div class="stat-mini-label">本月收入</div>
                                        </div>
                                        <div class="stat-mini">
                                            <div class="stat-mini-value">¥156,800</div>
                                            <div class="stat-mini-label">年度收入</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 收费类型分布 -->
                                <div>
                                    <h4 class="font-medium mb-2">收费类型分布</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-blue-500 rounded"></div>
                                                <span class="text-sm">临时停车</span>
                                            </div>
                                            <span class="text-sm font-medium">65%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 65%"></div>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-green-500 rounded"></div>
                                                <span class="text-sm">月卡续费</span>
                                            </div>
                                            <span class="text-sm font-medium">25%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar success" style="width: 25%"></div>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                                                <span class="text-sm">访客停车</span>
                                            </div>
                                            <span class="text-sm font-medium">10%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar warning" style="width: 10%"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 支付方式统计 -->
                                <div>
                                    <h4 class="font-medium mb-2">支付方式</h4>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span>微信支付</span>
                                            <span class="font-medium">45%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>支付宝</span>
                                            <span class="font-medium">30%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>银行卡</span>
                                            <span class="font-medium">15%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>现金</span>
                                            <span class="font-medium">10%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 最近交易 -->
                                <div>
                                    <h4 class="font-medium mb-2">最近交易</h4>
                                    <div class="space-y-2">
                                        <div class="transaction-item">
                                            <div class="flex justify-between">
                                                <span class="text-sm">京A12345</span>
                                                <span class="text-sm font-medium text-green-600">+¥15</span>
                                            </div>
                                            <div class="text-xs text-gray-500">临时停车 · 2小时</div>
                                        </div>
                                        <div class="transaction-item">
                                            <div class="flex justify-between">
                                                <span class="text-sm">京B67890</span>
                                                <span class="text-sm font-medium text-green-600">+¥200</span>
                                            </div>
                                            <div class="text-xs text-gray-500">月卡续费 · 1个月</div>
                                        </div>
                                        <div class="transaction-item">
                                            <div class="flex justify-between">
                                                <span class="text-sm">京C11111</span>
                                                <span class="text-sm font-medium text-green-600">+¥8</span>
                                            </div>
                                            <div class="text-xs text-gray-500">访客停车 · 1小时</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化停车管理页面
        SmartBuilding.initializePage('停车管理系统', ['智能服务', '停车管理']);

        // 车位点击事件
        document.querySelectorAll('.parking-spot').forEach(spot => {
            spot.addEventListener('click', function() {
                const spotId = this.dataset.spot;
                const isOccupied = this.classList.contains('occupied');

                if (isOccupied) {
                    alert(`车位 ${spotId} 已被占用`);
                } else {
                    alert(`车位 ${spotId} 空闲可用`);
                }
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            // 随机更新一些车位状态
            const spots = document.querySelectorAll('.parking-spot');
            const randomSpot = spots[Math.floor(Math.random() * spots.length)];

            if (Math.random() > 0.8) { // 20% 概率更新状态
                if (randomSpot.classList.contains('occupied')) {
                    randomSpot.classList.remove('occupied');
                    randomSpot.classList.add('available');
                } else if (randomSpot.classList.contains('available')) {
                    randomSpot.classList.remove('available');
                    randomSpot.classList.add('occupied');
                }
            }
        }, 10000);
    </script>

    <style>
        /* 停车场布局样式 */
        .parking-layout {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            min-height: 400px;
        }

        .parking-entrance,
        .parking-exit {
            position: absolute;
            background: var(--primary-light);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .parking-entrance {
            top: 20px;
            left: 20px;
        }

        .parking-exit {
            bottom: 20px;
            right: 20px;
        }

        .parking-zone {
            margin: 60px 20px 20px 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            background: white;
        }

        .zone-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            text-align: center;
        }

        .parking-spots {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
        }

        .parking-spot {
            width: 50px;
            height: 30px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #ddd;
        }

        .parking-spot.occupied {
            background: #ef4444;
            color: white;
            border-color: #dc2626;
        }

        .parking-spot.available {
            background: #10b981;
            color: white;
            border-color: #059669;
        }

        .parking-spot.reserved {
            background: #f59e0b;
            color: white;
            border-color: #d97706;
        }

        .parking-spot.disabled {
            background: #6b7280;
            color: white;
            border-color: #4b5563;
        }

        .parking-spot:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* 图例样式 */
        .parking-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #ddd;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .legend-color {
            width: 16px;
            height: 12px;
            border-radius: 2px;
        }

        .legend-color.occupied {
            background: #ef4444;
        }

        .legend-color.available {
            background: #10b981;
        }

        .legend-color.reserved {
            background: #f59e0b;
        }

        .legend-color.disabled {
            background: #6b7280;
        }

        /* 监控面板样式 */
        .monitor-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid var(--border-color);
        }

        .monitor-screen {
            background: #1a1a1a;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            color: #666;
            margin-bottom: 8px;
        }

        .monitor-info {
            space-y: 4px;
        }

        /* 车辆项样式 */
        .vehicle-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .vehicle-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--primary-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .vehicle-icon.warning {
            background: var(--warning-color);
        }

        .vehicle-plate {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
        }

        .vehicle-info {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .vehicle-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 统计小卡片 */
        .stat-mini {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-mini-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-mini-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 交易项样式 */
        .transaction-item {
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .btn-xs {
            padding: 4px 8px;
            font-size: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .col-span-2 {
            grid-column: span 2;
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }

        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-1 {
            gap: 0.25rem;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-3 {
            gap: 0.75rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .flex-1 {
            flex: 1;
        }

        .form-select {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }
    </style>
</body>
</html>