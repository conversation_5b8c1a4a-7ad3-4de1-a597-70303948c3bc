<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>楼宇自动化系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">楼宇自动化系统</h1>
                    <p class="page-subtitle">智能控制空调、照明、通风等楼宇设备系统</p>
                </div>
                
                <!-- 系统概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-snowflake"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                正常
                            </div>
                        </div>
                        <div class="stat-value" data-realtime="temperature">24°C</div>
                        <div class="stat-label">平均温度</div>
                        <div class="stat-change">目标温度 26°C</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                节能模式
                            </div>
                        </div>
                        <div class="stat-value">85%</div>
                        <div class="stat-label">照明开启率</div>
                        <div class="stat-change">智能调光运行中</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-wind"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                正常
                            </div>
                        </div>
                        <div class="stat-value" data-realtime="humidity">65%</div>
                        <div class="stat-label">湿度水平</div>
                        <div class="stat-change">正常范围内</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon info">
                                <i class="fas fa-elevator"></i>
                            </div>
                            <div class="stat-status warning">
                                <div class="stat-status-dot"></div>
                                维护中
                            </div>
                        </div>
                        <div class="stat-value">4/5</div>
                        <div class="stat-label">电梯运行</div>
                        <div class="stat-change">1台维护中</div>
                    </div>
                </div>
                
                <!-- 主要控制面板 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 空调系统控制 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-snowflake mr-2"></i>
                                空调系统控制
                            </h3>
                            <span class="status online">
                                <span class="status-dot"></span>
                                运行中
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 温度控制 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">目标温度</span>
                                    <div class="flex items-center gap-2">
                                        <button class="btn btn-outline btn-sm" onclick="adjustTemperature(-1)">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <span class="text-lg font-bold w-12 text-center" id="targetTemp">26</span>
                                        <button class="btn btn-outline btn-sm" onclick="adjustTemperature(1)">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <span class="text-sm text-gray-500">°C</span>
                                    </div>
                                </div>
                                
                                <!-- 模式选择 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">运行模式</span>
                                    <select class="form-select" id="acMode">
                                        <option value="auto">自动模式</option>
                                        <option value="cool" selected>制冷模式</option>
                                        <option value="heat">制热模式</option>
                                        <option value="fan">送风模式</option>
                                    </select>
                                </div>
                                
                                <!-- 风速控制 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">风速等级</span>
                                    <div class="flex gap-2">
                                        <button class="btn btn-outline btn-sm active">低</button>
                                        <button class="btn btn-outline btn-sm">中</button>
                                        <button class="btn btn-outline btn-sm">高</button>
                                        <button class="btn btn-outline btn-sm">自动</button>
                                    </div>
                                </div>
                                
                                <!-- 区域控制 -->
                                <div class="space-y-2">
                                    <span class="font-medium">区域控制</span>
                                    <div class="grid grid-cols-2 gap-2">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-sm">1F 大厅</span>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-sm">2F 办公区</span>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-sm">3F 会议室</span>
                                            <label class="switch">
                                                <input type="checkbox">
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-sm">4F 办公区</span>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 智能照明控制 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-lightbulb mr-2"></i>
                                智能照明控制
                            </h3>
                            <span class="status online">
                                <span class="status-dot"></span>
                                节能模式
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 总体控制 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">总体亮度</span>
                                    <div class="flex items-center gap-2">
                                        <input type="range" min="0" max="100" value="75" class="slider-range" id="brightness">
                                        <span class="text-sm text-gray-500 w-8">75%</span>
                                    </div>
                                </div>
                                
                                <!-- 场景模式 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">场景模式</span>
                                    <select class="form-select" id="lightScene">
                                        <option value="work" selected>工作模式</option>
                                        <option value="meeting">会议模式</option>
                                        <option value="rest">休息模式</option>
                                        <option value="energy">节能模式</option>
                                    </select>
                                </div>
                                
                                <!-- 自动控制 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">自动调光</span>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                
                                <!-- 区域照明 -->
                                <div class="space-y-2">
                                    <span class="font-medium">区域照明</span>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <span class="text-sm">大厅照明</span>
                                                <span class="text-xs text-gray-500">85%</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <span class="text-sm">办公区照明</span>
                                                <span class="text-xs text-gray-500">90%</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <span class="text-sm">走廊照明</span>
                                                <span class="text-xs text-gray-500">60%</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <span class="text-sm">应急照明</span>
                                                <span class="text-xs text-gray-500">100%</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 通风系统和电梯监控 -->
                <div class="grid grid-cols-2 gap-6 mt-6">
                    <!-- 通风系统 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-wind mr-2"></i>
                                通风系统
                            </h3>
                            <span class="status warning">
                                <span class="status-dot"></span>
                                维护模式
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">新风系统</span>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">排风系统</span>
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">空气质量</span>
                                    <span class="text-sm text-green-600">优秀</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">PM2.5</span>
                                    <span class="text-sm text-gray-600">12 μg/m³</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 电梯监控 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-elevator mr-2"></i>
                                电梯监控
                            </h3>
                            <span class="status online">
                                <span class="status-dot"></span>
                                4/5 运行
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium">电梯 A</span>
                                        <span class="text-xs text-gray-500">3F</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        运行中
                                    </span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium">电梯 B</span>
                                        <span class="text-xs text-gray-500">1F</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        待机
                                    </span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium">电梯 C</span>
                                        <span class="text-xs text-gray-500">2F</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        运行中
                                    </span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium">电梯 D</span>
                                        <span class="text-xs text-gray-500">4F</span>
                                    </div>
                                    <span class="status online">
                                        <span class="status-dot"></span>
                                        运行中
                                    </span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium">电梯 E</span>
                                        <span class="text-xs text-gray-500">--</span>
                                    </div>
                                    <span class="status offline">
                                        <span class="status-dot"></span>
                                        维护中
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化楼宇自动化页面
        SmartBuilding.initializePage('楼宇自动化系统', ['楼宇自动化']);
        
        // 温度调节功能
        function adjustTemperature(delta) {
            const tempElement = document.getElementById('targetTemp');
            let currentTemp = parseInt(tempElement.textContent);
            currentTemp += delta;
            if (currentTemp >= 16 && currentTemp <= 30) {
                tempElement.textContent = currentTemp;
            }
        }
        
        // 亮度调节功能
        document.getElementById('brightness').addEventListener('input', function(e) {
            const value = e.target.value;
            e.target.nextElementSibling.textContent = value + '%';
        });
    </script>
    
    <style>
        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-light);
        }
        
        input:checked + .slider:before {
            transform: translateX(20px);
        }
        
        /* 滑块样式 */
        .slider-range {
            width: 100px;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-light);
            cursor: pointer;
        }
        
        .form-select {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
        
        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }
        
        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }
    </style>
</body>
</html>
