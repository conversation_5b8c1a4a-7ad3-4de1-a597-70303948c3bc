<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门禁管理系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">门禁管理系统</h1>
                    <p class="page-subtitle">智能门禁控制，多种认证方式，实时监控通行状态</p>
                </div>
                
                <!-- 门禁概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-door-open"></i>
                            </div>
                        </div>
                        <div class="stat-value">12/12</div>
                        <div class="stat-label">门禁在线</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>100% 在线</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value">1,247</div>
                        <div class="stat-label">授权用户</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>23 新增</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-sign-in-alt"></i>
                            </div>
                        </div>
                        <div class="stat-value">156</div>
                        <div class="stat-label">今日通行</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>较昨日 +12</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value">2</div>
                        <div class="stat-label">异常记录</div>
                        <div class="stat-change negative">
                            <i class="fas fa-shield-alt"></i>
                            <span>需要处理</span>
                        </div>
                    </div>
                </div>
                
                <!-- 门禁控制和状态监控 -->
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 门禁设备控制 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-door-closed mr-2"></i>
                                门禁设备控制
                            </h3>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i>
                                添加设备
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="door-control-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="door-icon">
                                                <i class="fas fa-door-open"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium">主入口大门</div>
                                                <div class="text-sm text-gray-500">设备ID: DOOR001</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                开启
                                            </span>
                                            <button class="btn btn-outline btn-sm" onclick="toggleDoor('DOOR001')">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="door-control-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="door-icon">
                                                <i class="fas fa-door-closed"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium">停车场入口</div>
                                                <div class="text-sm text-gray-500">设备ID: DOOR002</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="status offline">
                                                <span class="status-dot"></span>
                                                关闭
                                            </span>
                                            <button class="btn btn-outline btn-sm" onclick="toggleDoor('DOOR002')">
                                                <i class="fas fa-unlock"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="door-control-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="door-icon">
                                                <i class="fas fa-door-open"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium">办公区入口</div>
                                                <div class="text-sm text-gray-500">设备ID: DOOR003</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                开启
                                            </span>
                                            <button class="btn btn-outline btn-sm" onclick="toggleDoor('DOOR003')">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="door-control-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="door-icon">
                                                <i class="fas fa-door-open"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium">会议室区域</div>
                                                <div class="text-sm text-gray-500">设备ID: DOOR004</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                开启
                                            </span>
                                            <button class="btn btn-outline btn-sm" onclick="toggleDoor('DOOR004')">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="door-control-item">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="door-icon">
                                                <i class="fas fa-door-closed"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium">安全出口</div>
                                                <div class="text-sm text-gray-500">设备ID: DOOR005</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="status warning">
                                                <span class="status-dot"></span>
                                                维护
                                            </span>
                                            <button class="btn btn-outline btn-sm" disabled>
                                                <i class="fas fa-tools"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权限管理 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-key mr-2"></i>
                                权限管理
                            </h3>
                            <button class="btn btn-success btn-sm">
                                <i class="fas fa-user-plus"></i>
                                添加用户
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 权限组管理 -->
                                <div>
                                    <h4 class="font-medium mb-2">权限组</h4>
                                    <div class="grid grid-cols-2 gap-2">
                                        <div class="permission-group">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium">管理员</span>
                                                <span class="badge primary">15人</span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">全部区域访问权限</div>
                                        </div>
                                        <div class="permission-group">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium">员工</span>
                                                <span class="badge success">1,156人</span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">办公区域访问权限</div>
                                        </div>
                                        <div class="permission-group">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium">访客</span>
                                                <span class="badge warning">76人</span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">临时访问权限</div>
                                        </div>
                                        <div class="permission-group">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-medium">安保</span>
                                                <span class="badge danger">8人</span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">安全区域访问权限</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 认证方式 -->
                                <div>
                                    <h4 class="font-medium mb-2">认证方式</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-id-card text-blue-500"></i>
                                                <span class="text-sm">IC卡认证</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-user text-green-500"></i>
                                                <span class="text-sm">人脸识别</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-qrcode text-orange-500"></i>
                                                <span class="text-sm">二维码</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-fingerprint text-purple-500"></i>
                                                <span class="text-sm">指纹识别</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox">
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 时间段控制 -->
                                <div>
                                    <h4 class="font-medium mb-2">时间段控制</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">工作日 (周一-周五)</span>
                                            <span class="text-sm text-gray-600">08:00 - 18:00</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">周末 (周六-周日)</span>
                                            <span class="text-sm text-gray-600">09:00 - 17:00</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">节假日</span>
                                            <span class="text-sm text-gray-600">仅管理员</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 通行记录和异常告警 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 实时通行记录 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-history mr-2"></i>
                                实时通行记录
                            </h3>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="access-record">
                                    <div class="flex items-center gap-3">
                                        <div class="record-avatar success">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="record-name">张三</div>
                                            <div class="record-detail">主入口大门 · IC卡认证</div>
                                            <div class="record-time">刚刚</div>
                                        </div>
                                        <span class="badge success">进入</span>
                                    </div>
                                </div>
                                
                                <div class="access-record">
                                    <div class="flex items-center gap-3">
                                        <div class="record-avatar warning">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="record-name">李四</div>
                                            <div class="record-detail">办公区入口 · 人脸识别</div>
                                            <div class="record-time">2分钟前</div>
                                        </div>
                                        <span class="badge warning">离开</span>
                                    </div>
                                </div>
                                
                                <div class="access-record">
                                    <div class="flex items-center gap-3">
                                        <div class="record-avatar primary">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="record-name">王五</div>
                                            <div class="record-detail">会议室区域 · 二维码</div>
                                            <div class="record-time">5分钟前</div>
                                        </div>
                                        <span class="badge success">进入</span>
                                    </div>
                                </div>
                                
                                <div class="access-record">
                                    <div class="flex items-center gap-3">
                                        <div class="record-avatar danger">
                                            <i class="fas fa-user-times"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="record-name">未知用户</div>
                                            <div class="record-detail">主入口大门 · 认证失败</div>
                                            <div class="record-time">10分钟前</div>
                                        </div>
                                        <span class="badge danger">拒绝</span>
                                    </div>
                                </div>
                                
                                <div class="access-record">
                                    <div class="flex items-center gap-3">
                                        <div class="record-avatar success">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="record-name">赵六</div>
                                            <div class="record-detail">停车场入口 · IC卡认证</div>
                                            <div class="record-time">15分钟前</div>
                                        </div>
                                        <span class="badge success">进入</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 异常告警 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-shield-alt mr-2"></i>
                                异常告警
                            </h3>
                            <span class="badge danger">2 未处理</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="alert-item danger">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon danger">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">多次认证失败</div>
                                            <div class="alert-desc">主入口大门检测到连续5次认证失败</div>
                                            <div class="alert-time">5分钟前 · DOOR001</div>
                                        </div>
                                        <button class="btn btn-danger btn-sm">处理</button>
                                    </div>
                                </div>
                                
                                <div class="alert-item warning">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon warning">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">非工作时间访问</div>
                                            <div class="alert-desc">检测到非工作时间有人员尝试进入办公区</div>
                                            <div class="alert-time">1小时前 · DOOR003</div>
                                        </div>
                                        <button class="btn btn-warning btn-sm">查看</button>
                                    </div>
                                </div>
                                
                                <div class="alert-item info">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon info">
                                            <i class="fas fa-tools"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">设备维护提醒</div>
                                            <div class="alert-desc">安全出口门禁设备需要定期维护</div>
                                            <div class="alert-time">2小时前 · DOOR005</div>
                                        </div>
                                        <span class="text-sm text-gray-500">已安排</span>
                                    </div>
                                </div>
                                
                                <div class="alert-item success">
                                    <div class="flex items-start gap-3">
                                        <div class="alert-icon success">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="alert-title">系统自检完成</div>
                                            <div class="alert-desc">所有门禁设备自检正常</div>
                                            <div class="alert-time">3小时前 · 系统</div>
                                        </div>
                                        <span class="text-sm text-gray-500">已完成</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化门禁管理页面
        SmartBuilding.initializePage('门禁管理系统', ['安全管理', '门禁管理']);
        
        // 门禁控制功能
        function toggleDoor(doorId) {
            const doorItem = document.querySelector(`[onclick="toggleDoor('${doorId}')"]`);
            const statusElement = doorItem.parentElement.querySelector('.status');
            const iconElement = doorItem.querySelector('i');
            
            if (statusElement.classList.contains('online')) {
                statusElement.classList.remove('online');
                statusElement.classList.add('offline');
                statusElement.innerHTML = '<span class="status-dot"></span>关闭';
                iconElement.className = 'fas fa-unlock';
            } else {
                statusElement.classList.remove('offline');
                statusElement.classList.add('online');
                statusElement.innerHTML = '<span class="status-dot"></span>开启';
                iconElement.className = 'fas fa-lock';
            }
        }
        
        // 模拟实时通行记录更新
        setInterval(() => {
            // 这里可以添加实时更新通行记录的逻辑
        }, 10000);
    </script>
    
    <style>
        /* 门禁控制项样式 */
        .door-control-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .door-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--primary-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        /* 权限组样式 */
        .permission-group {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        /* 通行记录样式 */
        .access-record {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid transparent;
        }
        
        .record-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .record-avatar.success {
            background-color: var(--success-color);
        }
        
        .record-avatar.warning {
            background-color: var(--warning-color);
        }
        
        .record-avatar.primary {
            background-color: var(--primary-light);
        }
        
        .record-avatar.danger {
            background-color: var(--danger-color);
        }
        
        .record-name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .record-detail {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .record-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-light);
        }
        
        input:checked + .slider:before {
            transform: translateX(20px);
        }
        
        .space-y-4 > * + * {
            margin-top: 1rem;
        }
        
        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }
        
        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
    </style>
</body>
</html>
