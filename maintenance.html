<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运维管理系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">运维管理系统</h1>
                    <p class="page-subtitle">设备维护、工单管理、系统监控一体化运维平台</p>
                </div>
                
                <!-- 运维概览 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-tools"></i>
                            </div>
                        </div>
                        <div class="stat-value">156</div>
                        <div class="stat-label">设备总数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>运行正常</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value">8</div>
                        <div class="stat-label">待处理工单</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-up"></i>
                            <span>3 紧急</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                        <div class="stat-value">12</div>
                        <div class="stat-label">今日巡检</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i>
                            <span>已完成 8</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                        </div>
                        <div class="stat-value">98.5%</div>
                        <div class="stat-label">系统健康度</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>优秀</span>
                        </div>
                    </div>
                </div>
                
                <!-- 工单管理和设备状态 -->
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 工单管理 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clipboard-list mr-2"></i>
                                工单管理
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i>
                                    新建工单
                                </button>
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-filter"></i>
                                    筛选
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="work-order urgent">
                                    <div class="work-order-header">
                                        <div class="work-order-id">#WO001</div>
                                        <span class="priority-badge urgent">紧急</span>
                                    </div>
                                    <div class="work-order-title">空调系统制冷效果异常</div>
                                    <div class="work-order-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> 3F办公区</span>
                                        <span><i class="fas fa-user"></i> 张三</span>
                                        <span><i class="fas fa-clock"></i> 2小时前</span>
                                    </div>
                                    <div class="work-order-actions">
                                        <button class="btn btn-primary btn-sm">接单</button>
                                        <button class="btn btn-outline btn-sm">详情</button>
                                    </div>
                                </div>
                                
                                <div class="work-order high">
                                    <div class="work-order-header">
                                        <div class="work-order-id">#WO002</div>
                                        <span class="priority-badge high">高</span>
                                    </div>
                                    <div class="work-order-title">电梯运行噪音过大</div>
                                    <div class="work-order-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> 电梯A</span>
                                        <span><i class="fas fa-user"></i> 李四</span>
                                        <span><i class="fas fa-clock"></i> 4小时前</span>
                                    </div>
                                    <div class="work-order-actions">
                                        <button class="btn btn-primary btn-sm">接单</button>
                                        <button class="btn btn-outline btn-sm">详情</button>
                                    </div>
                                </div>
                                
                                <div class="work-order medium">
                                    <div class="work-order-header">
                                        <div class="work-order-id">#WO003</div>
                                        <span class="priority-badge medium">中</span>
                                    </div>
                                    <div class="work-order-title">会议室投影仪无法开机</div>
                                    <div class="work-order-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> 会议室A01</span>
                                        <span><i class="fas fa-user"></i> 王五</span>
                                        <span><i class="fas fa-clock"></i> 1天前</span>
                                    </div>
                                    <div class="work-order-actions">
                                        <button class="btn btn-warning btn-sm">处理中</button>
                                        <button class="btn btn-outline btn-sm">详情</button>
                                    </div>
                                </div>
                                
                                <div class="work-order low">
                                    <div class="work-order-header">
                                        <div class="work-order-id">#WO004</div>
                                        <span class="priority-badge low">低</span>
                                    </div>
                                    <div class="work-order-title">办公区照明灯泡更换</div>
                                    <div class="work-order-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> 2F办公区</span>
                                        <span><i class="fas fa-user"></i> 赵六</span>
                                        <span><i class="fas fa-clock"></i> 2天前</span>
                                    </div>
                                    <div class="work-order-actions">
                                        <button class="btn btn-success btn-sm">已完成</button>
                                        <button class="btn btn-outline btn-sm">详情</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 设备状态监控 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-server mr-2"></i>
                                设备状态监控
                            </h3>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-sync"></i>
                                刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="equipment-category">
                                    <div class="category-header">
                                        <h4 class="category-title">空调系统</h4>
                                        <span class="category-status">5/6 正常</span>
                                    </div>
                                    <div class="equipment-list">
                                        <div class="equipment-item">
                                            <div class="equipment-info">
                                                <span class="equipment-name">中央空调#1</span>
                                                <span class="equipment-location">1F大厅</span>
                                            </div>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                        <div class="equipment-item">
                                            <div class="equipment-info">
                                                <span class="equipment-name">中央空调#2</span>
                                                <span class="equipment-location">2F办公区</span>
                                            </div>
                                            <span class="status warning">
                                                <span class="status-dot"></span>
                                                异常
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="equipment-category">
                                    <div class="category-header">
                                        <h4 class="category-title">电梯系统</h4>
                                        <span class="category-status">4/5 正常</span>
                                    </div>
                                    <div class="equipment-list">
                                        <div class="equipment-item">
                                            <div class="equipment-info">
                                                <span class="equipment-name">电梯A</span>
                                                <span class="equipment-location">主楼</span>
                                            </div>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                        <div class="equipment-item">
                                            <div class="equipment-info">
                                                <span class="equipment-name">电梯B</span>
                                                <span class="equipment-location">主楼</span>
                                            </div>
                                            <span class="status offline">
                                                <span class="status-dot"></span>
                                                维护
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="equipment-category">
                                    <div class="category-header">
                                        <h4 class="category-title">照明系统</h4>
                                        <span class="category-status">全部正常</span>
                                    </div>
                                    <div class="equipment-list">
                                        <div class="equipment-item">
                                            <div class="equipment-info">
                                                <span class="equipment-name">LED控制器#1</span>
                                                <span class="equipment-location">1-3F</span>
                                            </div>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="equipment-category">
                                    <div class="category-header">
                                        <h4 class="category-title">安防系统</h4>
                                        <span class="category-status">全部正常</span>
                                    </div>
                                    <div class="equipment-list">
                                        <div class="equipment-item">
                                            <div class="equipment-info">
                                                <span class="equipment-name">监控服务器</span>
                                                <span class="equipment-location">机房</span>
                                            </div>
                                            <span class="status online">
                                                <span class="status-dot"></span>
                                                正常
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 巡检计划和维护记录 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 巡检计划 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-route mr-2"></i>
                                巡检计划
                            </h3>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i>
                                新建巡检
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="inspection-item completed">
                                    <div class="inspection-header">
                                        <div class="inspection-title">空调系统日常巡检</div>
                                        <span class="inspection-status completed">已完成</span>
                                    </div>
                                    <div class="inspection-meta">
                                        <span><i class="fas fa-user"></i> 张三</span>
                                        <span><i class="fas fa-clock"></i> 09:00-10:00</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 全楼</span>
                                    </div>
                                    <div class="inspection-progress">
                                        <div class="progress">
                                            <div class="progress-bar success" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-text">6/6 完成</span>
                                    </div>
                                </div>
                                
                                <div class="inspection-item in-progress">
                                    <div class="inspection-header">
                                        <div class="inspection-title">电梯系统安全检查</div>
                                        <span class="inspection-status in-progress">进行中</span>
                                    </div>
                                    <div class="inspection-meta">
                                        <span><i class="fas fa-user"></i> 李四</span>
                                        <span><i class="fas fa-clock"></i> 14:00-16:00</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 电梯区</span>
                                    </div>
                                    <div class="inspection-progress">
                                        <div class="progress">
                                            <div class="progress-bar warning" style="width: 60%"></div>
                                        </div>
                                        <span class="progress-text">3/5 完成</span>
                                    </div>
                                </div>
                                
                                <div class="inspection-item pending">
                                    <div class="inspection-header">
                                        <div class="inspection-title">消防设备月检</div>
                                        <span class="inspection-status pending">待开始</span>
                                    </div>
                                    <div class="inspection-meta">
                                        <span><i class="fas fa-user"></i> 王五</span>
                                        <span><i class="fas fa-clock"></i> 16:00-18:00</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 全楼</span>
                                    </div>
                                    <div class="inspection-progress">
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 0%"></div>
                                        </div>
                                        <span class="progress-text">0/8 完成</span>
                                    </div>
                                </div>
                                
                                <div class="inspection-item pending">
                                    <div class="inspection-header">
                                        <div class="inspection-title">网络设备巡检</div>
                                        <span class="inspection-status pending">待开始</span>
                                    </div>
                                    <div class="inspection-meta">
                                        <span><i class="fas fa-user"></i> 赵六</span>
                                        <span><i class="fas fa-clock"></i> 明天 09:00</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 机房</span>
                                    </div>
                                    <div class="inspection-progress">
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 0%"></div>
                                        </div>
                                        <span class="progress-text">0/4 完成</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 维护记录 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-history mr-2"></i>
                                维护记录
                            </h3>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <div class="maintenance-record">
                                    <div class="record-header">
                                        <div class="record-title">空调系统清洁保养</div>
                                        <span class="record-date">2024-01-15</span>
                                    </div>
                                    <div class="record-details">
                                        <div class="record-meta">
                                            <span><i class="fas fa-tools"></i> 定期保养</span>
                                            <span><i class="fas fa-user"></i> 张三</span>
                                            <span><i class="fas fa-clock"></i> 2小时</span>
                                        </div>
                                        <div class="record-description">
                                            更换空调滤网，清洁冷凝器，检查制冷剂压力
                                        </div>
                                    </div>
                                    <span class="record-status completed">已完成</span>
                                </div>
                                
                                <div class="maintenance-record">
                                    <div class="record-header">
                                        <div class="record-title">电梯钢丝绳检查</div>
                                        <span class="record-date">2024-01-14</span>
                                    </div>
                                    <div class="record-details">
                                        <div class="record-meta">
                                            <span><i class="fas fa-search"></i> 安全检查</span>
                                            <span><i class="fas fa-user"></i> 李四</span>
                                            <span><i class="fas fa-clock"></i> 1.5小时</span>
                                        </div>
                                        <div class="record-description">
                                            检查电梯钢丝绳磨损情况，润滑导轨，测试安全装置
                                        </div>
                                    </div>
                                    <span class="record-status completed">已完成</span>
                                </div>
                                
                                <div class="maintenance-record">
                                    <div class="record-header">
                                        <div class="record-title">UPS电源维护</div>
                                        <span class="record-date">2024-01-13</span>
                                    </div>
                                    <div class="record-details">
                                        <div class="record-meta">
                                            <span><i class="fas fa-battery-half"></i> 电池更换</span>
                                            <span><i class="fas fa-user"></i> 王五</span>
                                            <span><i class="fas fa-clock"></i> 3小时</span>
                                        </div>
                                        <div class="record-description">
                                            更换UPS蓄电池，测试备电时间，校准电压参数
                                        </div>
                                    </div>
                                    <span class="record-status completed">已完成</span>
                                </div>
                                
                                <div class="maintenance-record">
                                    <div class="record-header">
                                        <div class="record-title">消防系统测试</div>
                                        <span class="record-date">2024-01-12</span>
                                    </div>
                                    <div class="record-details">
                                        <div class="record-meta">
                                            <span><i class="fas fa-fire-extinguisher"></i> 功能测试</span>
                                            <span><i class="fas fa-user"></i> 赵六</span>
                                            <span><i class="fas fa-clock"></i> 4小时</span>
                                        </div>
                                        <div class="record-description">
                                            测试烟感报警器，检查消防栓水压，验证自动喷淋系统
                                        </div>
                                    </div>
                                    <span class="record-status completed">已完成</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化运维管理页面
        SmartBuilding.initializePage('运维管理系统', ['运维管理']);
        
        // 工单接单功能
        document.querySelectorAll('.work-order .btn-primary').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent.includes('接单')) {
                    this.textContent = '处理中';
                    this.className = 'btn btn-warning btn-sm';
                    alert('工单已接单，开始处理');
                }
            });
        });
        
        // 模拟实时数据更新
        setInterval(() => {
            // 更新设备状态
            const equipmentItems = document.querySelectorAll('.equipment-item');
            equipmentItems.forEach(item => {
                const status = item.querySelector('.status');
                if (Math.random() > 0.95) { // 5% 概率状态变化
                    if (status.classList.contains('online')) {
                        status.classList.remove('online');
                        status.classList.add('warning');
                        status.innerHTML = '<span class="status-dot"></span>异常';
                    } else if (status.classList.contains('warning')) {
                        status.classList.remove('warning');
                        status.classList.add('online');
                        status.innerHTML = '<span class="status-dot"></span>正常';
                    }
                }
            });
        }, 15000);
    </script>

    <style>
        /* 工单样式 */
        .work-order {
            padding: 16px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid transparent;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .work-order:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .work-order.urgent {
            border-left-color: #dc2626;
            background: #fef2f2;
        }

        .work-order.high {
            border-left-color: #ea580c;
            background: #fff7ed;
        }

        .work-order.medium {
            border-left-color: #d97706;
            background: #fffbeb;
        }

        .work-order.low {
            border-left-color: #16a34a;
            background: #f0fdf4;
        }

        .work-order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .work-order-id {
            font-weight: 600;
            color: var(--text-primary);
            font-family: monospace;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-badge.urgent {
            background: #dc2626;
            color: white;
        }

        .priority-badge.high {
            background: #ea580c;
            color: white;
        }

        .priority-badge.medium {
            background: #d97706;
            color: white;
        }

        .priority-badge.low {
            background: #16a34a;
            color: white;
        }

        .work-order-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 16px;
        }

        .work-order-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .work-order-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .work-order-actions {
            display: flex;
            gap: 8px;
        }

        /* 设备分类样式 */
        .equipment-category {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .category-header {
            background: #f8f9fa;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .category-title {
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .category-status {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .equipment-list {
            padding: 8px;
        }

        .equipment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .equipment-item:hover {
            background: #f8f9fa;
        }

        .equipment-info {
            display: flex;
            flex-direction: column;
        }

        .equipment-name {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 14px;
        }

        .equipment-location {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 巡检项样式 */
        .inspection-item {
            padding: 16px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid transparent;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .inspection-item.completed {
            border-left-color: var(--success-color);
            background: #f0fdf4;
        }

        .inspection-item.in-progress {
            border-left-color: var(--warning-color);
            background: #fffbeb;
        }

        .inspection-item.pending {
            border-left-color: var(--border-color);
        }

        .inspection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .inspection-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .inspection-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .inspection-status.completed {
            background: var(--success-color);
            color: white;
        }

        .inspection-status.in-progress {
            background: var(--warning-color);
            color: white;
        }

        .inspection-status.pending {
            background: var(--border-color);
            color: var(--text-secondary);
        }

        .inspection-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .inspection-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .inspection-progress {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .inspection-progress .progress {
            flex: 1;
        }

        .progress-text {
            font-size: 12px;
            color: var(--text-secondary);
            white-space: nowrap;
        }

        /* 维护记录样式 */
        .maintenance-record {
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            position: relative;
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .record-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .record-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .record-details {
            margin-bottom: 8px;
        }

        .record-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 8px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .record-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .record-description {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .record-status {
            position: absolute;
            top: 16px;
            right: 16px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .record-status.completed {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }

        .flex {
            display: flex;
        }

        .flex-wrap {
            flex-wrap: wrap;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .gap-8 {
            gap: 2rem;
        }

        .gap-12 {
            gap: 3rem;
        }

        @media (max-width: 768px) {
            .work-order-meta,
            .inspection-meta,
            .record-meta {
                flex-direction: column;
                gap: 4px;
            }

            .work-order-actions {
                flex-direction: column;
            }

            .inspection-progress {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }
        }
    </style>
</body>
</html>
