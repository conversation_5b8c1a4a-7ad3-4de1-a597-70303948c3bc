<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警管理系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">报警管理系统</h1>
                    <p class="page-subtitle">整合各类安防感知设备，实现对可疑行为的快速响应与预警</p>
                </div>
                
                <!-- 系统概览统计 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon danger">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-status warning">
                                <div class="stat-status-dot"></div>
                                告警中
                            </div>
                        </div>
                        <div class="stat-value">3</div>
                        <div class="stat-label">活跃告警</div>
                        <div class="stat-change">需要处理</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                布防中
                            </div>
                        </div>
                        <div class="stat-value">156</div>
                        <div class="stat-label">防护设备</div>
                        <div class="stat-change">在线监控</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                正常
                            </div>
                        </div>
                        <div class="stat-value">89</div>
                        <div class="stat-label">消防设备</div>
                        <div class="stat-change">状态良好</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon info">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                进行中
                            </div>
                        </div>
                        <div class="stat-value">12</div>
                        <div class="stat-label">巡更任务</div>
                        <div class="stat-change">按计划执行</div>
                    </div>
                </div>
                
                <!-- 入侵报警和消防报警 -->
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 入侵报警集成 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-secret mr-2"></i>
                                入侵报警集成
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-success btn-sm" onclick="toggleDefense('arm')">
                                    <i class="fas fa-shield-alt"></i>
                                    布防
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="toggleDefense('disarm')">
                                    <i class="fas fa-shield-alt"></i>
                                    撤防
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 报警设备状态 -->
                                <div class="device-section">
                                    <h4 class="font-medium mb-2">报警设备状态</h4>
                                    <div class="device-grid">
                                        <div class="device-item">
                                            <i class="fas fa-door-open text-green-500"></i>
                                            <span class="text-sm">门磁传感器</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">24/24在线</span>
                                        </div>
                                        <div class="device-item">
                                            <i class="fas fa-walking text-blue-500"></i>
                                            <span class="text-sm">红外感应器</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">18/18在线</span>
                                        </div>
                                        <div class="device-item">
                                            <i class="fas fa-window-maximize text-orange-500"></i>
                                            <span class="text-sm">振动传感器</span>
                                            <span class="badge warning">异常</span>
                                            <span class="text-xs">11/12在线</span>
                                        </div>
                                        <div class="device-item">
                                            <i class="fas fa-glass-martini text-red-500"></i>
                                            <span class="text-sm">破玻器</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">8/8在线</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 布防策略 -->
                                <div class="defense-section">
                                    <h4 class="font-medium mb-2">布防策略配置</h4>
                                    <div class="strategy-controls">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">自动布防时间</span>
                                            <select class="form-select-sm">
                                                <option>18:00 - 08:00</option>
                                                <option>19:00 - 07:00</option>
                                                <option>20:00 - 06:00</option>
                                            </select>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">布防区域</span>
                                            <div class="flex gap-1">
                                                <span class="badge primary">1-5F</span>
                                                <span class="badge success">地下室</span>
                                                <span class="badge warning">天台</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 最近报警记录 -->
                                <div class="alarm-records">
                                    <h4 class="font-medium mb-2">最近报警记录</h4>
                                    <div class="record-list">
                                        <div class="record-item urgent">
                                            <div class="record-time">15:30</div>
                                            <div class="record-info">
                                                <span class="record-type">门磁异常</span>
                                                <span class="record-location">3F-办公区-301</span>
                                            </div>
                                            <span class="badge danger">未处理</span>
                                        </div>
                                        <div class="record-item">
                                            <div class="record-time">14:15</div>
                                            <div class="record-info">
                                                <span class="record-type">红外触发</span>
                                                <span class="record-location">1F-大厅-东侧</span>
                                            </div>
                                            <span class="badge success">已处理</span>
                                        </div>
                                        <div class="record-item">
                                            <div class="record-time">12:45</div>
                                            <div class="record-info">
                                                <span class="record-type">振动报警</span>
                                                <span class="record-location">2F-会议室-窗户</span>
                                            </div>
                                            <span class="badge primary">已确认</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 消防报警系统 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-fire-extinguisher mr-2"></i>
                                消防报警系统
                            </h3>
                            <span class="status online">
                                <span class="status-dot"></span>
                                系统正常
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 消防设备状态 -->
                                <div class="fire-devices">
                                    <h4 class="font-medium mb-2">消防设备状态</h4>
                                    <div class="device-grid">
                                        <div class="device-item">
                                            <i class="fas fa-smoke text-gray-600"></i>
                                            <span class="text-sm">烟雾探测器</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">45/45在线</span>
                                        </div>
                                        <div class="device-item">
                                            <i class="fas fa-thermometer-half text-red-500"></i>
                                            <span class="text-sm">温感探测器</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">32/32在线</span>
                                        </div>
                                        <div class="device-item">
                                            <i class="fas fa-tint text-blue-500"></i>
                                            <span class="text-sm">消火栓系统</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">水压正常</span>
                                        </div>
                                        <div class="device-item">
                                            <i class="fas fa-water text-blue-600"></i>
                                            <span class="text-sm">水池液位</span>
                                            <span class="badge success">正常</span>
                                            <span class="text-xs">85%液位</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 消防主机状态 -->
                                <div class="fire-control">
                                    <h4 class="font-medium mb-2">消防主机状态</h4>
                                    <div class="control-status">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">海湾主机A</span>
                                            <span class="badge success">在线</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">利达主机B</span>
                                            <span class="badge success">在线</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">泰和安主机C</span>
                                            <span class="badge warning">维护</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 自动响应配置 -->
                                <div class="auto-response">
                                    <h4 class="font-medium mb-2">自动响应配置</h4>
                                    <div class="response-options">
                                        <div class="option-item">
                                            <input type="checkbox" id="broadcast" checked>
                                            <label for="broadcast">联动广播疏散</label>
                                        </div>
                                        <div class="option-item">
                                            <input type="checkbox" id="video" checked>
                                            <label for="video">视频联动推送</label>
                                        </div>
                                        <div class="option-item">
                                            <input type="checkbox" id="elevator">
                                            <label for="elevator">电梯迫降</label>
                                        </div>
                                        <div class="option-item">
                                            <input type="checkbox" id="access" checked>
                                            <label for="access">门禁自动开启</label>
                                        </div>
                                        <div class="option-item">
                                            <input type="checkbox" id="smoke" checked>
                                            <label for="smoke">排烟系统启动</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 联动机制和巡更系统 -->
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 多维度联动机制 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-link mr-2"></i>
                                多维度联动机制
                            </h3>
                            <span class="badge success">6个设备联动</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 联动设备配置 -->
                                <div class="linkage-devices">
                                    <h4 class="font-medium mb-2">联动设备配置</h4>
                                    <div class="device-linkage-grid">
                                        <div class="linkage-item">
                                            <div class="linkage-icon">
                                                <i class="fas fa-video text-blue-500"></i>
                                            </div>
                                            <div class="linkage-info">
                                                <span class="text-sm font-medium">视频监控</span>
                                                <span class="text-xs text-gray-500">自动弹出画面</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="linkage-item">
                                            <div class="linkage-icon">
                                                <i class="fas fa-door-open text-green-500"></i>
                                            </div>
                                            <div class="linkage-info">
                                                <span class="text-sm font-medium">门禁控制</span>
                                                <span class="text-xs text-gray-500">自动调整状态</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="linkage-item">
                                            <div class="linkage-icon">
                                                <i class="fas fa-elevator text-purple-500"></i>
                                            </div>
                                            <div class="linkage-info">
                                                <span class="text-sm font-medium">电梯控制</span>
                                                <span class="text-xs text-gray-500">封锁或解锁</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox">
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <div class="linkage-item">
                                            <div class="linkage-icon">
                                                <i class="fas fa-broadcast-tower text-orange-500"></i>
                                            </div>
                                            <div class="linkage-info">
                                                <span class="text-sm font-medium">智能广播</span>
                                                <span class="text-xs text-gray-500">应急灯联动</span>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" checked>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 智能推送配置 -->
                                <div class="push-config">
                                    <h4 class="font-medium mb-2">智能推送配置</h4>
                                    <div class="push-channels">
                                        <div class="channel-item">
                                            <i class="fas fa-sms text-green-600"></i>
                                            <span class="text-sm">短信推送</span>
                                            <span class="badge success">已启用</span>
                                        </div>
                                        <div class="channel-item">
                                            <i class="fab fa-weixin text-green-500"></i>
                                            <span class="text-sm">微信推送</span>
                                            <span class="badge success">已启用</span>
                                        </div>
                                        <div class="channel-item">
                                            <i class="fas fa-comments text-blue-500"></i>
                                            <span class="text-sm">企业IM</span>
                                            <span class="badge warning">待配置</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 推送等级设置 -->
                                <div class="push-levels">
                                    <h4 class="font-medium mb-2">推送等级设置</h4>
                                    <div class="level-config">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">一级告警</span>
                                            <span class="text-xs text-red-600">总经理、安保主管</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">二级告警</span>
                                            <span class="text-xs text-orange-600">部门经理、值班人员</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">三级告警</span>
                                            <span class="text-xs text-blue-600">安保人员、维护人员</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 安全巡更系统 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-route mr-2"></i>
                                安全巡更系统
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-primary btn-sm" onclick="createPatrolRoute()">
                                    <i class="fas fa-plus"></i>
                                    新建路线
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 巡更路线配置 -->
                                <div class="patrol-routes">
                                    <h4 class="font-medium mb-2">巡更路线配置</h4>
                                    <div class="route-list">
                                        <div class="route-item">
                                            <div class="route-info">
                                                <span class="text-sm font-medium">夜间巡更路线A</span>
                                                <span class="text-xs text-gray-500">1F→2F→3F→天台 (8个检查点)</span>
                                            </div>
                                            <div class="route-status">
                                                <span class="badge success">进行中</span>
                                                <span class="text-xs">22:00-06:00</span>
                                            </div>
                                        </div>
                                        <div class="route-item">
                                            <div class="route-info">
                                                <span class="text-sm font-medium">日间巡更路线B</span>
                                                <span class="text-xs text-gray-500">地下室→1F→设备间 (5个检查点)</span>
                                            </div>
                                            <div class="route-status">
                                                <span class="badge primary">待执行</span>
                                                <span class="text-xs">08:00-18:00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 智能打卡设备 -->
                                <div class="checkin-devices">
                                    <h4 class="font-medium mb-2">智能打卡设备</h4>
                                    <div class="device-status-grid">
                                        <div class="device-status-item">
                                            <i class="fas fa-mobile-alt text-blue-500"></i>
                                            <span class="text-sm">移动端NFC</span>
                                            <span class="badge success">在线</span>
                                        </div>
                                        <div class="device-status-item">
                                            <i class="fas fa-user-check text-green-500"></i>
                                            <span class="text-sm">人脸识别</span>
                                            <span class="badge success">在线</span>
                                        </div>
                                        <div class="device-status-item">
                                            <i class="fas fa-qrcode text-purple-500"></i>
                                            <span class="text-sm">扫码设备</span>
                                            <span class="badge warning">离线</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 巡更异常统计 -->
                                <div class="patrol-anomalies">
                                    <h4 class="font-medium mb-2">巡更异常统计</h4>
                                    <div class="anomaly-stats">
                                        <div class="stat-row">
                                            <span class="text-sm">未巡检查点</span>
                                            <span class="text-lg font-bold text-red-600">2</span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="text-sm">迟到次数</span>
                                            <span class="text-lg font-bold text-orange-600">1</span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="text-sm">完成率</span>
                                            <span class="text-lg font-bold text-green-600">94%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 当前巡更状态 -->
                                <div class="current-patrol">
                                    <h4 class="font-medium mb-2">当前巡更状态</h4>
                                    <div class="patrol-progress">
                                        <div class="progress-info">
                                            <span class="text-sm">张三 - 夜间巡更路线A</span>
                                            <span class="text-xs text-gray-500">进度: 6/8 检查点</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 75%"></div>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-500">
                                            <span>开始时间: 22:00</span>
                                            <span>预计完成: 23:30</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 集中报警可视化平台 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-desktop mr-2"></i>
                            集中报警可视化平台
                        </h3>
                        <div class="flex gap-2">
                            <button class="btn btn-outline btn-sm" onclick="exportReport()">
                                <i class="fas fa-download"></i>
                                导出报表
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="openFullScreen()">
                                <i class="fas fa-expand"></i>
                                全屏显示
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-3 gap-6">
                            <!-- 告警总览大屏 -->
                            <div class="col-span-2">
                                <h4 class="font-medium mb-3">告警总览大屏</h4>
                                <div class="alarm-map">
                                    <div class="map-container">
                                        <div class="building-layout">
                                            <!-- 楼层平面图模拟 -->
                                            <div class="floor-plan">
                                                <div class="floor-label">20F</div>
                                                <div class="alarm-point normal" data-location="20F-会议室"></div>
                                                <div class="alarm-point normal" data-location="20F-办公区"></div>
                                            </div>
                                            <div class="floor-plan">
                                                <div class="floor-label">15F</div>
                                                <div class="alarm-point normal" data-location="15F-办公区"></div>
                                                <div class="alarm-point warning" data-location="15F-茶水间"></div>
                                            </div>
                                            <div class="floor-plan">
                                                <div class="floor-label">10F</div>
                                                <div class="alarm-point normal" data-location="10F-办公区"></div>
                                                <div class="alarm-point normal" data-location="10F-会议室"></div>
                                            </div>
                                            <div class="floor-plan">
                                                <div class="floor-label">5F</div>
                                                <div class="alarm-point danger" data-location="5F-设备间"></div>
                                                <div class="alarm-point normal" data-location="5F-办公区"></div>
                                            </div>
                                            <div class="floor-plan">
                                                <div class="floor-label">1F</div>
                                                <div class="alarm-point normal" data-location="1F-大厅"></div>
                                                <div class="alarm-point processing" data-location="1F-安保室"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 图例 -->
                                    <div class="map-legend">
                                        <div class="legend-item">
                                            <div class="legend-dot normal"></div>
                                            <span>正常</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot warning"></div>
                                            <span>警告</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot danger"></div>
                                            <span>紧急</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot processing"></div>
                                            <span>处理中</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 事件流转和统计 -->
                            <div class="col-span-1">
                                <div class="space-y-4">
                                    <!-- 多级事件流转 -->
                                    <div class="event-flow">
                                        <h4 class="font-medium mb-2">事件流转机制</h4>
                                        <div class="flow-list">
                                            <div class="flow-item urgent">
                                                <div class="flow-header">
                                                    <span class="text-sm font-medium">5F设备间烟雾报警</span>
                                                    <span class="badge danger">紧急</span>
                                                </div>
                                                <div class="flow-actions">
                                                    <button class="btn btn-success btn-xs" onclick="assignAlarm(1)">分配</button>
                                                    <button class="btn btn-warning btn-xs" onclick="escalateAlarm(1)">上报</button>
                                                    <button class="btn btn-outline btn-xs" onclick="viewDetails(1)">详情</button>
                                                </div>
                                                <div class="flow-time">15:30 - 未处理</div>
                                            </div>

                                            <div class="flow-item">
                                                <div class="flow-header">
                                                    <span class="text-sm font-medium">1F安保室门禁异常</span>
                                                    <span class="badge warning">处理中</span>
                                                </div>
                                                <div class="flow-actions">
                                                    <button class="btn btn-primary btn-xs" onclick="closeAlarm(2)">关闭</button>
                                                    <button class="btn btn-outline btn-xs" onclick="viewDetails(2)">详情</button>
                                                </div>
                                                <div class="flow-time">14:15 - 张三处理中</div>
                                            </div>

                                            <div class="flow-item completed">
                                                <div class="flow-header">
                                                    <span class="text-sm font-medium">15F茶水间水浸报警</span>
                                                    <span class="badge success">已关闭</span>
                                                </div>
                                                <div class="flow-time">12:45 - 李四已处理</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 数据统计 -->
                                    <div class="alarm-statistics">
                                        <h4 class="font-medium mb-2">数据统计分析</h4>
                                        <div class="stats-grid">
                                            <div class="stat-item">
                                                <span class="stat-label">今日告警</span>
                                                <span class="stat-number">15</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">平均响应</span>
                                                <span class="stat-number">3.2分钟</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">处置效率</span>
                                                <span class="stat-number">92%</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">误报率</span>
                                                <span class="stat-number">5.8%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 告警类型分布 -->
                                    <div class="alarm-types">
                                        <h4 class="font-medium mb-2">告警类型分布</h4>
                                        <div class="type-chart">
                                            <div class="chart-item">
                                                <div class="chart-bar" style="width: 45%"></div>
                                                <span class="chart-label">入侵报警 45%</span>
                                            </div>
                                            <div class="chart-item">
                                                <div class="chart-bar fire" style="width: 25%"></div>
                                                <span class="chart-label">消防报警 25%</span>
                                            </div>
                                            <div class="chart-item">
                                                <div class="chart-bar system" style="width: 20%"></div>
                                                <span class="chart-label">系统异常 20%</span>
                                            </div>
                                            <div class="chart-item">
                                                <div class="chart-bar other" style="width: 10%"></div>
                                                <span class="chart-label">其他 10%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化报警管理页面
        SmartBuilding.initializePage('报警管理系统', ['智能服务', '报警管理']);
        
        // 布防/撤防控制
        function toggleDefense(action) {
            const message = action === 'arm' ? '确定要启动布防模式吗？' : '确定要撤销布防模式吗？';
            const successMsg = action === 'arm' ? '布防模式已启动' : '布防模式已撤销';
            
            if (confirm(message)) {
                SmartBuilding.showNotification(successMsg, 'success');
                updateDefenseStatus(action);
            }
        }
        
        // 更新布防状态
        function updateDefenseStatus(action) {
            const statusElements = document.querySelectorAll('.stat-status');
            statusElements.forEach(el => {
                if (el.textContent.includes('布防中') || el.textContent.includes('撤防中')) {
                    el.innerHTML = `<div class="stat-status-dot"></div>${action === 'arm' ? '布防中' : '撤防中'}`;
                    el.className = `stat-status ${action === 'arm' ? 'online' : 'warning'}`;
                }
            });
        }

        // 创建巡更路线
        function createPatrolRoute() {
            SmartBuilding.showNotification('巡更路线创建功能开发中...', 'info');
        }

        // 告警事件处理
        function assignAlarm(id) {
            if (confirm('确定要分配此告警事件吗？')) {
                SmartBuilding.showNotification('告警事件已分配给相关人员', 'success');
                updateAlarmStatus(id, 'assigned');
            }
        }

        function escalateAlarm(id) {
            if (confirm('确定要上报此告警事件吗？')) {
                SmartBuilding.showNotification('告警事件已上报至上级管理员', 'warning');
                updateAlarmStatus(id, 'escalated');
            }
        }

        function closeAlarm(id) {
            if (confirm('确定要关闭此告警事件吗？')) {
                SmartBuilding.showNotification('告警事件已关闭', 'success');
                updateAlarmStatus(id, 'closed');
            }
        }

        function viewDetails(id) {
            SmartBuilding.showNotification('告警详情查看功能开发中...', 'info');
        }

        // 更新告警状态
        function updateAlarmStatus(id, status) {
            const flowItems = document.querySelectorAll('.flow-item');
            flowItems.forEach(item => {
                if (item.dataset.id == id) {
                    const badge = item.querySelector('.badge');
                    switch(status) {
                        case 'assigned':
                            badge.textContent = '已分配';
                            badge.className = 'badge primary';
                            break;
                        case 'escalated':
                            badge.textContent = '已上报';
                            badge.className = 'badge warning';
                            break;
                        case 'closed':
                            badge.textContent = '已关闭';
                            badge.className = 'badge success';
                            item.classList.add('completed');
                            break;
                    }
                }
            });
        }

        // 导出报表
        function exportReport() {
            SmartBuilding.showNotification('报表导出功能开发中...', 'info');
        }

        // 全屏显示
        function openFullScreen() {
            SmartBuilding.showNotification('全屏显示功能开发中...', 'info');
        }

        // 告警点击事件
        document.querySelectorAll('.alarm-point').forEach(point => {
            point.addEventListener('click', function() {
                const location = this.dataset.location;
                SmartBuilding.showNotification(`查看 ${location} 详细信息`, 'info');
            });
        });

        // 实时数据更新模拟
        function updateRealTimeData() {
            // 模拟告警数量变化
            const alarmCount = document.querySelector('.stat-value');
            if (alarmCount && Math.random() > 0.8) {
                let current = parseInt(alarmCount.textContent);
                alarmCount.textContent = Math.max(0, current + (Math.random() > 0.5 ? 1 : -1));
            }
        }

        // 每30秒更新一次实时数据
        setInterval(updateRealTimeData, 30000);
    </script>

    <style>
        /* 报警管理专用样式 */
        .device-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .device-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .strategy-controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .form-select-sm {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background: var(--card-bg);
            font-size: 12px;
        }

        .record-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .record-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s;
        }

        .record-item:hover {
            background: var(--border-light);
        }

        .record-item.urgent {
            border-left: 4px solid var(--danger-color);
            background: rgba(239, 68, 68, 0.05);
        }

        .record-time {
            font-size: 12px;
            color: var(--text-secondary);
            min-width: 40px;
        }

        .record-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .record-type {
            font-weight: 500;
            font-size: 13px;
        }

        .record-location {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .control-status {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .response-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .option-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .option-item input[type="checkbox"] {
            margin: 0;
        }

        .device-linkage-grid {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .linkage-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .linkage-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius);
            background: var(--card-bg);
        }

        .linkage-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .push-channels {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .channel-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--card-bg);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .level-config {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .route-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .route-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .route-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .route-status {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .device-status-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 8px;
        }

        .device-status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--card-bg);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .anomaly-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .patrol-progress {
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .progress-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 8px;
        }

        /* 告警地图样式 */
        .alarm-map {
            position: relative;
        }

        .map-container {
            height: 300px;
            background: var(--border-light);
            border-radius: var(--radius);
            padding: 20px;
            overflow-y: auto;
        }

        .building-layout {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .floor-plan {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 12px;
            background: var(--card-bg);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .floor-label {
            font-weight: bold;
            min-width: 40px;
            text-align: center;
        }

        .alarm-point {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .alarm-point:hover {
            transform: scale(1.2);
        }

        .alarm-point.normal {
            background: var(--success-color);
        }

        .alarm-point.warning {
            background: var(--warning-color);
            animation: pulse 2s infinite;
        }

        .alarm-point.danger {
            background: var(--danger-color);
            animation: pulse 1s infinite;
        }

        .alarm-point.processing {
            background: var(--primary-color);
            animation: pulse 1.5s infinite;
        }

        .map-legend {
            display: flex;
            gap: 16px;
            margin-top: 12px;
            padding: 8px;
            background: var(--card-bg);
            border-radius: var(--radius);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .legend-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .legend-dot.normal {
            background: var(--success-color);
        }

        .legend-dot.warning {
            background: var(--warning-color);
        }

        .legend-dot.danger {
            background: var(--danger-color);
        }

        .legend-dot.processing {
            background: var(--primary-color);
        }

        .flow-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .flow-item {
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .flow-item.urgent {
            border-left: 4px solid var(--danger-color);
            background: rgba(239, 68, 68, 0.05);
        }

        .flow-item.completed {
            opacity: 0.7;
        }

        .flow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .flow-actions {
            display: flex;
            gap: 4px;
            margin-bottom: 4px;
        }

        .flow-time {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            text-align: center;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-primary);
        }

        .type-chart {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .chart-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-bar {
            height: 16px;
            background: var(--primary-color);
            border-radius: var(--radius);
            transition: width 0.3s ease;
        }

        .chart-bar.fire {
            background: var(--danger-color);
        }

        .chart-bar.system {
            background: var(--warning-color);
        }

        .chart-bar.other {
            background: var(--text-secondary);
        }

        .chart-label {
            font-size: 12px;
            color: var(--text-secondary);
            min-width: 80px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-6 {
            gap: 1.5rem;
        }

        .col-span-2 {
            grid-column: span 2;
        }

        .col-span-1 {
            grid-column: span 1;
        }

        .text-xs {
            font-size: 12px;
        }

        .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
        }
    </style>
</body>
</html>
