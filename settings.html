<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">系统设置</h1>
                    <p class="page-subtitle">系统配置、用户管理、安全设置等系统管理功能</p>
                </div>
                
                <!-- 设置导航 -->
                <div class="settings-nav mb-6">
                    <button class="nav-tab active" data-tab="general">
                        <i class="fas fa-cog"></i>
                        常规设置
                    </button>
                    <button class="nav-tab" data-tab="users">
                        <i class="fas fa-users"></i>
                        用户管理
                    </button>
                    <button class="nav-tab" data-tab="security">
                        <i class="fas fa-shield-alt"></i>
                        安全设置
                    </button>
                    <button class="nav-tab" data-tab="system">
                        <i class="fas fa-server"></i>
                        系统信息
                    </button>
                </div>
                
                <!-- 常规设置 -->
                <div class="tab-content active" id="general">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-building mr-2"></i>
                                    楼宇信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <form class="space-y-4">
                                    <div>
                                        <label class="form-label">楼宇名称</label>
                                        <input type="text" class="form-input" value="智慧科技大厦" required>
                                    </div>
                                    <div>
                                        <label class="form-label">地址</label>
                                        <input type="text" class="form-input" value="北京市朝阳区科技园区123号" required>
                                    </div>
                                    <div>
                                        <label class="form-label">联系电话</label>
                                        <input type="tel" class="form-input" value="010-12345678" required>
                                    </div>
                                    <div>
                                        <label class="form-label">管理公司</label>
                                        <input type="text" class="form-input" value="智慧物业管理有限公司" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        保存设置
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-clock mr-2"></i>
                                    时间设置
                                </h3>
                            </div>
                            <div class="card-body">
                                <form class="space-y-4">
                                    <div>
                                        <label class="form-label">时区</label>
                                        <select class="form-select" required>
                                            <option value="Asia/Shanghai" selected>中国标准时间 (UTC+8)</option>
                                            <option value="UTC">协调世界时 (UTC)</option>
                                            <option value="America/New_York">美国东部时间 (UTC-5)</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="form-label">日期格式</label>
                                        <select class="form-select" required>
                                            <option value="YYYY-MM-DD" selected>2024-01-15</option>
                                            <option value="DD/MM/YYYY">15/01/2024</option>
                                            <option value="MM/DD/YYYY">01/15/2024</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="form-label">时间格式</label>
                                        <select class="form-select" required>
                                            <option value="24" selected>24小时制</option>
                                            <option value="12">12小时制</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="form-label">工作时间</label>
                                        <div class="time-range">
                                            <input type="time" class="form-input" value="08:00">
                                            <span>至</span>
                                            <input type="time" class="form-input" value="18:00">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        保存设置
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-6 mt-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-bell mr-2"></i>
                                    通知设置
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="space-y-4">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">系统告警通知</div>
                                            <div class="setting-desc">设备故障、安全告警等重要通知</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">邮件通知</div>
                                            <div class="setting-desc">通过邮件发送重要通知</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">短信通知</div>
                                            <div class="setting-desc">紧急情况下发送短信通知</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">微信通知</div>
                                            <div class="setting-desc">通过企业微信发送通知</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-database mr-2"></i>
                                    数据设置
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="space-y-4">
                                    <div>
                                        <label class="form-label">数据保留期限</label>
                                        <select class="form-select">
                                            <option value="30">30天</option>
                                            <option value="90" selected>90天</option>
                                            <option value="180">180天</option>
                                            <option value="365">1年</option>
                                            <option value="0">永久保留</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="form-label">自动备份</label>
                                        <select class="form-select">
                                            <option value="daily" selected>每日备份</option>
                                            <option value="weekly">每周备份</option>
                                            <option value="monthly">每月备份</option>
                                            <option value="manual">手动备份</option>
                                        </select>
                                    </div>
                                    <div class="backup-actions">
                                        <button class="btn btn-outline">
                                            <i class="fas fa-download"></i>
                                            立即备份
                                        </button>
                                        <button class="btn btn-outline">
                                            <i class="fas fa-upload"></i>
                                            恢复数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 用户管理 -->
                <div class="tab-content" id="users">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users mr-2"></i>
                                用户管理
                            </h3>
                            <button class="btn btn-primary">
                                <i class="fas fa-user-plus"></i>
                                添加用户
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>姓名</th>
                                            <th>角色</th>
                                            <th>邮箱</th>
                                            <th>状态</th>
                                            <th>最后登录</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>admin</td>
                                            <td>系统管理员</td>
                                            <td><span class="badge danger">超级管理员</span></td>
                                            <td><EMAIL></td>
                                            <td><span class="status online"><span class="status-dot"></span>在线</span></td>
                                            <td>刚刚</td>
                                            <td>
                                                <button class="btn btn-outline btn-sm">编辑</button>
                                                <button class="btn btn-outline btn-sm" disabled>删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>manager</td>
                                            <td>张三</td>
                                            <td><span class="badge warning">管理员</span></td>
                                            <td><EMAIL></td>
                                            <td><span class="status online"><span class="status-dot"></span>在线</span></td>
                                            <td>10分钟前</td>
                                            <td>
                                                <button class="btn btn-outline btn-sm">编辑</button>
                                                <button class="btn btn-outline btn-sm">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>operator</td>
                                            <td>李四</td>
                                            <td><span class="badge primary">操作员</span></td>
                                            <td><EMAIL></td>
                                            <td><span class="status offline"><span class="status-dot"></span>离线</span></td>
                                            <td>2小时前</td>
                                            <td>
                                                <button class="btn btn-outline btn-sm">编辑</button>
                                                <button class="btn btn-outline btn-sm">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>viewer</td>
                                            <td>王五</td>
                                            <td><span class="badge success">查看者</span></td>
                                            <td><EMAIL></td>
                                            <td><span class="status online"><span class="status-dot"></span>在线</span></td>
                                            <td>30分钟前</td>
                                            <td>
                                                <button class="btn btn-outline btn-sm">编辑</button>
                                                <button class="btn btn-outline btn-sm">删除</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 安全设置 -->
                <div class="tab-content" id="security">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-lock mr-2"></i>
                                    密码策略
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="space-y-4">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">密码最小长度</div>
                                            <div class="setting-desc">设置密码最小字符数</div>
                                        </div>
                                        <select class="form-select" style="width: auto;">
                                            <option value="6">6位</option>
                                            <option value="8" selected>8位</option>
                                            <option value="10">10位</option>
                                            <option value="12">12位</option>
                                        </select>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">密码复杂度</div>
                                            <div class="setting-desc">要求包含大小写字母、数字和特殊字符</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">密码过期时间</div>
                                            <div class="setting-desc">密码定期更换周期</div>
                                        </div>
                                        <select class="form-select" style="width: auto;">
                                            <option value="30">30天</option>
                                            <option value="60">60天</option>
                                            <option value="90" selected>90天</option>
                                            <option value="0">永不过期</option>
                                        </select>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">登录失败锁定</div>
                                            <div class="setting-desc">连续登录失败后锁定账户</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    访问控制
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="space-y-4">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">IP白名单</div>
                                            <div class="setting-desc">限制特定IP地址访问</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">双因子认证</div>
                                            <div class="setting-desc">启用短信或邮箱验证码</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">会话超时</div>
                                            <div class="setting-desc">无操作自动退出时间</div>
                                        </div>
                                        <select class="form-select" style="width: auto;">
                                            <option value="15">15分钟</option>
                                            <option value="30" selected>30分钟</option>
                                            <option value="60">1小时</option>
                                            <option value="120">2小时</option>
                                        </select>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-title">操作日志</div>
                                            <div class="setting-desc">记录用户操作日志</div>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="tab-content" id="system">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    系统信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="system-info">
                                    <div class="info-item">
                                        <span class="info-label">系统版本</span>
                                        <span class="info-value">v2.1.0</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">构建时间</span>
                                        <span class="info-value">2024-01-15 10:30:00</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">运行时间</span>
                                        <span class="info-value">15天 8小时 32分钟</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">数据库版本</span>
                                        <span class="info-value">MySQL 8.0.28</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">服务器</span>
                                        <span class="info-value">Ubuntu 20.04 LTS</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">许可证</span>
                                        <span class="info-value">企业版 (有效期至 2024-12-31)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-pie mr-2"></i>
                                    系统资源
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="space-y-4">
                                    <div class="resource-item">
                                        <div class="resource-header">
                                            <span class="resource-label">CPU使用率</span>
                                            <span class="resource-value">25%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar success" style="width: 25%"></div>
                                        </div>
                                    </div>
                                    <div class="resource-item">
                                        <div class="resource-header">
                                            <span class="resource-label">内存使用率</span>
                                            <span class="resource-value">68%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar warning" style="width: 68%"></div>
                                        </div>
                                    </div>
                                    <div class="resource-item">
                                        <div class="resource-header">
                                            <span class="resource-label">磁盘使用率</span>
                                            <span class="resource-value">45%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar success" style="width: 45%"></div>
                                        </div>
                                    </div>
                                    <div class="resource-item">
                                        <div class="resource-header">
                                            <span class="resource-label">网络流量</span>
                                            <span class="resource-value">156 MB/s</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar primary" style="width: 30%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/common.js"></script>
    <script>
        // 初始化系统设置页面
        SmartBuilding.initializePage('系统设置', ['系统设置']);
        
        // 标签页切换功能
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.dataset.tab;
                
                // 移除所有活动状态
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加当前活动状态
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
        
        // 表单提交处理
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('设置已保存！');
            });
        });
        
        // 模拟系统资源实时更新
        setInterval(() => {
            const cpuBar = document.querySelector('.resource-item:nth-child(1) .progress-bar');
            const memoryBar = document.querySelector('.resource-item:nth-child(2) .progress-bar');
            
            if (cpuBar) {
                const cpuValue = Math.floor(Math.random() * 40) + 10;
                cpuBar.style.width = cpuValue + '%';
                cpuBar.parentElement.previousElementSibling.querySelector('.resource-value').textContent = cpuValue + '%';
            }
            
            if (memoryBar) {
                const memoryValue = Math.floor(Math.random() * 20) + 60;
                memoryBar.style.width = memoryValue + '%';
                memoryBar.parentElement.previousElementSibling.querySelector('.resource-value').textContent = memoryValue + '%';
            }
        }, 5000);
    </script>

    <style>
        /* 设置导航样式 */
        .settings-nav {
            display: flex;
            gap: 4px;
            background: #f8f9fa;
            padding: 4px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .nav-tab {
            flex: 1;
            padding: 12px 16px;
            background: transparent;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .nav-tab:hover {
            background: white;
            color: var(--text-primary);
        }

        .nav-tab.active {
            background: var(--primary-light);
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        /* 标签页内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .form-input,
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .time-range {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .time-range input {
            flex: 1;
        }

        .time-range span {
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* 设置项样式 */
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .setting-info {
            flex: 1;
        }

        .setting-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .setting-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-light);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 备份操作按钮 */
        .backup-actions {
            display: flex;
            gap: 12px;
        }

        .backup-actions .btn {
            flex: 1;
        }

        /* 表格容器 */
        .table-container {
            overflow-x: auto;
        }

        /* 系统信息样式 */
        .system-info {
            space-y: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .info-value {
            color: var(--text-secondary);
            font-family: monospace;
        }

        /* 资源监控样式 */
        .resource-item {
            margin-bottom: 16px;
        }

        .resource-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .resource-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .resource-value {
            font-weight: 600;
            color: var(--text-secondary);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .mt-6 {
            margin-top: 1.5rem;
        }

        @media (max-width: 768px) {
            .settings-nav {
                flex-direction: column;
            }

            .nav-tab {
                justify-content: flex-start;
            }

            .time-range {
                flex-direction: column;
                align-items: stretch;
            }

            .backup-actions {
                flex-direction: column;
            }

            .setting-item {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
        }
    </style>
</body>
</html>
