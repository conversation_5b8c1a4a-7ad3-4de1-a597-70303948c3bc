<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梯控管理系统 - 智慧楼宇管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏容器 -->
        <div id="sidebarContainer"></div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏容器 -->
            <div id="topBarContainer"></div>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">梯控管理系统</h1>
                    <p class="page-subtitle">实现高效、安全的楼层控制和访客管理，保障电梯运行安全</p>
                </div>
                
                <!-- 系统概览统计 -->
                <div class="grid grid-cols-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-elevator"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                运行中
                            </div>
                        </div>
                        <div class="stat-value">5/6</div>
                        <div class="stat-label">电梯运行</div>
                        <div class="stat-change">1台维护中</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                正常
                            </div>
                        </div>
                        <div class="stat-value">142</div>
                        <div class="stat-label">今日载客</div>
                        <div class="stat-change">较昨日 +18</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                监控中
                            </div>
                        </div>
                        <div class="stat-value">1.2kW</div>
                        <div class="stat-label">实时功耗</div>
                        <div class="stat-change">平均功耗</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon info">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-status online">
                                <div class="stat-status-dot"></div>
                                安全
                            </div>
                        </div>
                        <div class="stat-value">98.5%</div>
                        <div class="stat-label">安全指数</div>
                        <div class="stat-change">运行稳定</div>
                    </div>
                </div>
                
                <!-- 主要功能区域 -->
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 身份联动呼梯 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-id-card mr-2"></i>
                                身份联动呼梯
                            </h3>
                            <span class="status online">
                                <span class="status-dot"></span>
                                运行中
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 身份识别状态 -->
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">身份识别系统</span>
                                    <span class="badge success">在线</span>
                                </div>
                                
                                <!-- 权限控制 -->
                                <div class="space-y-2">
                                    <span class="font-medium">楼层权限控制</span>
                                    <div class="grid grid-cols-3 gap-2">
                                        <div class="permission-item">
                                            <span class="text-sm">员工权限</span>
                                            <span class="text-xs text-green-600">1-20F</span>
                                        </div>
                                        <div class="permission-item">
                                            <span class="text-sm">访客权限</span>
                                            <span class="text-xs text-blue-600">1-5F</span>
                                        </div>
                                        <div class="permission-item">
                                            <span class="text-sm">VIP权限</span>
                                            <span class="text-xs text-purple-500">全楼层</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 实时呼梯记录 -->
                                <div class="space-y-2">
                                    <span class="font-medium">实时呼梯记录</span>
                                    <div class="elevator-log">
                                        <div class="log-item">
                                            <span class="text-sm">张三 (员工)</span>
                                            <span class="text-xs text-gray-500">1F → 15F</span>
                                            <span class="text-xs">刚刚</span>
                                        </div>
                                        <div class="log-item">
                                            <span class="text-sm">李四 (访客)</span>
                                            <span class="text-xs text-gray-500">1F → 3F</span>
                                            <span class="text-xs">2分钟前</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 紧急状态响应 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                紧急状态响应
                            </h3>
                            <span class="status online">
                                <span class="status-dot"></span>
                                待命
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 应急模式控制 -->
                                <div class="emergency-controls">
                                    <div class="control-group">
                                        <span class="font-medium">应急模式</span>
                                        <div class="flex gap-2">
                                            <button class="btn btn-danger btn-sm" onclick="triggerEmergency('fire')">
                                                <i class="fas fa-fire"></i>
                                                火灾模式
                                            </button>
                                            <button class="btn btn-warning btn-sm" onclick="triggerEmergency('power')">
                                                <i class="fas fa-power-off"></i>
                                                停电模式
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 系统状态监控 -->
                                <div class="space-y-2">
                                    <span class="font-medium">系统状态</span>
                                    <div class="status-grid">
                                        <div class="status-item">
                                            <i class="fas fa-fire text-red-500"></i>
                                            <span class="text-sm">火灾检测</span>
                                            <span class="badge success">正常</span>
                                        </div>
                                        <div class="status-item">
                                            <i class="fas fa-bolt text-yellow-500"></i>
                                            <span class="text-sm">电力系统</span>
                                            <span class="badge success">正常</span>
                                        </div>
                                        <div class="status-item">
                                            <i class="fas fa-weight-hanging text-orange-500"></i>
                                            <span class="text-sm">载重监控</span>
                                            <span class="badge success">正常</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 应急联系 -->
                                <div class="emergency-contact">
                                    <span class="font-medium">应急联系</span>
                                    <div class="flex gap-2">
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-phone"></i>
                                            维保中心
                                        </button>
                                        <button class="btn btn-outline btn-sm">
                                            <i class="fas fa-ambulance"></i>
                                            应急救援
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 运行监控和维保管理 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 运行与能耗监控 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line mr-2"></i>
                                运行与能耗监控
                            </h3>
                            <div class="flex gap-2">
                                <button class="btn btn-outline btn-sm">
                                    <i class="fas fa-download"></i>
                                    导出报告
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 实时运行数据 -->
                                <div class="monitoring-section">
                                    <h4 class="font-medium mb-2">实时运行数据</h4>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="metric-item">
                                            <span class="text-sm text-gray-600">运行次数</span>
                                            <span class="text-lg font-bold">1,247</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="text-sm text-gray-600">平均等待时间</span>
                                            <span class="text-lg font-bold">28秒</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="text-sm text-gray-600">载客率</span>
                                            <span class="text-lg font-bold">76%</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="text-sm text-gray-600">故障次数</span>
                                            <span class="text-lg font-bold text-red-600">0</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 能耗分析 -->
                                <div class="energy-section">
                                    <h4 class="font-medium mb-2">能耗分析</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm">今日能耗</span>
                                            <span class="font-medium">28.5 kWh</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 65%"></div>
                                        </div>
                                        <div class="flex items-center justify-between text-xs text-gray-500">
                                            <span>较昨日 -5.2%</span>
                                            <span>目标: 30 kWh</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 使用频率统计 -->
                                <div class="usage-section">
                                    <h4 class="font-medium mb-2">使用频率统计</h4>
                                    <div class="chart-container" style="height: 120px;">
                                        <div class="text-center">
                                            <i class="fas fa-chart-bar text-4xl text-gray-400 mb-2"></i>
                                            <p class="text-sm text-gray-500">使用频率图表</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 维保管理接口 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tools mr-2"></i>
                                维保管理接口
                            </h3>
                            <span class="badge warning">3 待处理</span>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <!-- 维保计划 -->
                                <div class="maintenance-section">
                                    <h4 class="font-medium mb-2">维保计划</h4>
                                    <div class="space-y-2">
                                        <div class="maintenance-item">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <span class="text-sm font-medium">电梯A - 月度保养</span>
                                                    <div class="text-xs text-gray-500">预计时间: 2小时</div>
                                                </div>
                                                <span class="badge warning">明天</span>
                                            </div>
                                        </div>
                                        <div class="maintenance-item">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <span class="text-sm font-medium">电梯B - 安全检查</span>
                                                    <div class="text-xs text-gray-500">预计时间: 1小时</div>
                                                </div>
                                                <span class="badge success">本周</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 工单管理 -->
                                <div class="workorder-section">
                                    <h4 class="font-medium mb-2">工单管理</h4>
                                    <div class="space-y-2">
                                        <div class="workorder-item">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <span class="text-sm font-medium">电梯C异响问题</span>
                                                    <div class="text-xs text-gray-500">报告时间: 今天 14:30</div>
                                                    <div class="text-xs text-red-600">优先级: 高</div>
                                                </div>
                                                <div class="flex flex-col gap-1">
                                                    <button class="btn btn-success btn-xs">处理</button>
                                                    <button class="btn btn-outline btn-xs">详情</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="workorder-item">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <span class="text-sm font-medium">按钮灯光故障</span>
                                                    <div class="text-xs text-gray-500">报告时间: 昨天 16:45</div>
                                                    <div class="text-xs text-yellow-600">优先级: 中</div>
                                                </div>
                                                <div class="flex flex-col gap-1">
                                                    <button class="btn btn-success btn-xs">处理</button>
                                                    <button class="btn btn-outline btn-xs">详情</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 异常日志 -->
                                <div class="log-section">
                                    <h4 class="font-medium mb-2">异常日志</h4>
                                    <div class="log-container">
                                        <div class="log-entry">
                                            <span class="text-xs text-gray-500">2024-06-23 15:30</span>
                                            <span class="text-sm">电梯A超载警告</span>
                                            <span class="badge warning">已处理</span>
                                        </div>
                                        <div class="log-entry">
                                            <span class="text-xs text-gray-500">2024-06-23 12:15</span>
                                            <span class="text-sm">电梯B门开超时</span>
                                            <span class="badge success">已解决</span>
                                        </div>
                                        <div class="log-entry">
                                            <span class="text-xs text-gray-500">2024-06-22 18:20</span>
                                            <span class="text-sm">系统自检完成</span>
                                            <span class="badge primary">正常</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化梯控管理页面
        SmartBuilding.initializePage('梯控管理系统', ['安全管理', '梯控管理']);
        
        // 紧急模式触发
        function triggerEmergency(type) {
            const messages = {
                fire: '火灾应急模式已启动！电梯将自动返回首层并停止运行。',
                power: '停电应急模式已启动！电梯将使用备用电源安全停靠。'
            };

            if (confirm(`确定要启动${type === 'fire' ? '火灾' : '停电'}应急模式吗？`)) {
                SmartBuilding.showNotification(messages[type], 'warning');

                // 更新应急状态显示
                updateEmergencyStatus(type);
            }
        }

        // 更新应急状态
        function updateEmergencyStatus(type) {
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach(el => {
                if (el.textContent.includes('待命')) {
                    el.innerHTML = '<span class="status-dot"></span>应急模式';
                    el.className = 'status warning';
                }
            });
        }

        // 工单处理
        document.querySelectorAll('.btn-success.btn-xs').forEach(btn => {
            if (btn.textContent.includes('处理')) {
                btn.addEventListener('click', function() {
                    const workorderItem = this.closest('.workorder-item');
                    const title = workorderItem.querySelector('.font-medium').textContent;

                    if (confirm(`确定要处理工单"${title}"吗？`)) {
                        SmartBuilding.showNotification('工单已分配给维保人员', 'success');
                        this.textContent = '已分配';
                        this.disabled = true;
                    }
                });
            }
        });

        // 实时数据更新模拟
        function updateRealTimeData() {
            const metrics = document.querySelectorAll('.metric-item .text-lg');
            if (metrics.length > 0) {
                // 模拟运行次数增加
                const runCount = metrics[0];
                let currentCount = parseInt(runCount.textContent.replace(',', ''));
                runCount.textContent = (currentCount + Math.floor(Math.random() * 3)).toLocaleString();

                // 模拟等待时间变化
                const waitTime = metrics[1];
                const newWaitTime = 25 + Math.floor(Math.random() * 10);
                waitTime.textContent = newWaitTime + '秒';

                // 模拟载客率变化
                const loadRate = metrics[2];
                const newLoadRate = 70 + Math.floor(Math.random() * 20);
                loadRate.textContent = newLoadRate + '%';
            }
        }

        // 每30秒更新一次实时数据
        setInterval(updateRealTimeData, 30000);
    </script>

    <style>
        /* 梯控管理专用样式 */
        .permission-item {
            padding: 8px 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            text-align: center;
        }

        .elevator-log {
            max-height: 120px;
            overflow-y: auto;
            background: var(--border-light);
            border-radius: var(--radius);
            padding: 8px;
        }

        .log-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .emergency-controls {
            padding: 12px;
            background: rgba(239, 68, 68, 0.05);
            border-radius: var(--radius);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .control-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-grid {
            display: grid;
            gap: 8px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .emergency-contact {
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .monitoring-section,
        .energy-section,
        .usage-section {
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .maintenance-item,
        .workorder-item {
            padding: 12px;
            background: var(--border-light);
            border-radius: var(--radius);
            border: 1px solid var(--border-color);
        }

        .log-container {
            max-height: 150px;
            overflow-y: auto;
            background: var(--border-light);
            border-radius: var(--radius);
            padding: 8px;
        }

        .log-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-start {
            align-items: flex-start;
        }

        .text-xs {
            font-size: 12px;
        }
    </style>
</body>
</html>
